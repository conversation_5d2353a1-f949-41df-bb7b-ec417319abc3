.group-head {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding-top: 30rpx;
}
.group-head-icon {
    font-size: 50rpx;
}

/* 已报考状态图标颜色 */
.cuIcon-roundcheckfill.group-head-icon {
    color: #0081ff;
}

/* 未报考状态图标颜色 */
.cuIcon-roundadd.group-head-icon {
    color: #333333;
}
.group-head text {
    font-size: 26rpx;
    color: #000;
    margin-left: 20rpx;
}
.course-layout {
    display: flex;
    flex-direction: row;
    align-items: center;
    background: white;
    border-radius: 30rpx;
    padding: 30rpx;
    margin-top: 30rpx;
}
.course-name {
    font-size: 30rpx;
    color: #333;
    flex: 1;
    margin-left: 5rpx;
    margin-right: 10rpx;
}
.course-layout image {
    width: 20rpx;
    padding: 15rpx;
}
.course-add {
    border-radius: 10rpx;
    background: #d53e44;
    color: white;
    font-size: 28rpx;
    text-align: center;
    padding: 20rpx 0;
    margin-top: 20rpx;
}
.wbk_hint {
    text-align: center;
    color: #999999;
    font-size: 28rpx;
    padding: 15rpx;
}
image {
    box-sizing: content-box;
}
.course-icon {
    font-size: 45rpx;
    padding: 20rpx;
}

/* 未报考状态显示主题色 */
.cuIcon-pullup.course-icon {
    color: #0081ff;
}

/* 已报考状态显示灰�?*/
.cuIcon-pulldown.course-icon {
    color: #8799a3;
}

/* PC端响应式样式 */
@media screen and (min-width: 768px) {
    /* 整体页面容器PC端适配 */
.course-container {
        max-width: 900px;
        margin: 15px auto;
        background-color: #ffffff;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.08);
        border-radius: 8px;
        min-height: calc(100vh - 30px);
        padding: 20px;
}

    /* 分组头部PC端适配 */
.group-head {
        padding-top: 20px;
}
.group-head-icon {
        font-size: 24px;
}
.group-head text {
        font-size: 14px;
        margin-left: 12px;
}

    /* 课程布局PC端适配 */
.course-layout {
        border-radius: 8px;
        padding: 16px 20px;
        margin-top: 12px;
        transition: all 0.3s ease;
        cursor: pointer;
}
.course-layout:hover {
        -webkit-transform: translateY(-1px);
                transform: translateY(-1px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.12);
}
.course-name {
        font-size: 14px;
        margin-left: 4px;
        margin-right: 8px;
}
.course-layout image {
        width: 12px;
        padding: 8px;
}
.course-icon {
        font-size: 20px;
        padding: 12px;
}

    /* 按钮PC端适配 */
.cu-btn {
        font-size: 14px;
        padding: 8px 16px;
        border-radius: 6px;
        min-height: 36px;
}

    /* 标题栏PC端适配 */
.cu-bar {
        padding: 8px 0;
        min-height: 50px;
}
.cu-bar .action {
        font-size: 14px;
}
}
