/* 移动端默认样式 (rpx单位) */
.menu-image {
	width: 56rpx;
	height: 56rpx;
	margin-bottom: 10rpx;
}
/* 移动端顶部搜索栏优化 - 缩小三个选择按钮 */
.cu-bar.search {
	min-height: 70rpx;
	padding: 8rpx 15rpx;
}
.cu-bar.search .action {
	font-size: 22rpx;
	padding: 6rpx 10rpx;
	max-width: 30%;
}
.cu-bar.search .action .cuIcon-location,
.cu-bar.search .action .cuIcon-group,
.cu-bar.search .action .cuIcon-read {
	font-size: 24rpx;
	margin-right: 4rpx;
}
/* 搜索栏动作按钮样式 */
.search-action-mobile {
	font-size: 24rpx;
	max-width: 33.3333%;
	padding: 6rpx 10rpx;
}
.search-action-pc {
	font-size: 14px;
	padding: 8px 12px;
	border-radius: 6px;
	background-color: rgba(255, 255, 255, 0.9);
	margin: 0 5px;
}
/* 移动端最新资讯卡片优化 */
.cu-card.article .cu-item {
	margin: 20rpx;
	border-radius: 16rpx;
}
.cu-card.article .content {
	padding: 24rpx;
}
.cu-card.article .content image {
	width: 120rpx;
	height: 120rpx;
	border-radius: 12rpx;
}
.cu-card.article .desc {
	margin-left: 20rpx;
	flex: 1;
}
.cu-card.article .text-df {
	font-size: 28rpx;
	line-height: 1.4;
	margin-bottom: 12rpx;
}
.cu-card.article .text-sm {
	font-size: 24rpx;
}
/* 移动端标题栏按钮优化 - 确保横向排列 */
.cu-bar .action {
	display: flex;
	flex-direction: row;
	align-items: center;
}
.cu-bar .action .cu-btn {
	font-size: 24rpx;
	padding: 10rpx 16rpx;
	min-height: 56rpx;
	display: flex;
	flex-direction: row;
	align-items: center;
}
.cu-bar .action .cu-btn .cuIcon-share,
.cu-bar .action .cu-btn .cuIcon-moreandroid {
	margin-left: 6rpx;
	font-size: 24rpx;
}
/* 移动端学习中心和最新资讯标题优化 */
.cu-bar .sub-title .text-xl {
	font-size: 32rpx;
}
.cu-bar .sub-title .text-ABC {
	font-size: 22rpx;
}
/* 移动端标题栏高度优化 */
.cu-bar {
	min-height: 80rpx;
	padding: 12rpx 30rpx;
}
/* 考试倒计时样式 */
.exam-countdown {
	padding: 0;
	border-radius: 12rpx;
	margin: 0 20rpx;
	overflow: hidden;
}
.countdown-content {
	background: linear-gradient(135deg, #f0f8ff, #e6f2ff);
	border-radius: 12rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 100, 255, 0.15);
	padding: 0;
	position: relative;
	overflow: hidden;
}
.countdown-header {
	background-color: #0081ff;
	color: #ffffff;
	padding: 16rpx 24rpx;
	display: flex;
	align-items: center;
}
.countdown-title {
	font-size: 28rpx;
	font-weight: 500;
	margin-left: 10rpx;
}
.countdown-info {
	padding: 20rpx 24rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.countdown-date {
	font-size: 26rpx;
	color: #555;
}
.countdown-timer {
	display: flex;
	align-items: center;
}
.countdown-days {
	font-size: 40rpx;
	font-weight: bold;
	color: #0081ff;
	background-color: rgba(0, 129, 255, 0.1);
	padding: 6rpx 16rpx;
	border-radius: 8rpx;
	margin-right: 8rpx;
}
.countdown-unit {
	font-size: 28rpx;
	color: #555;
}
/* 新的宫格列表样式 */
.modern-grid {
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	padding: 20rpx 10rpx;
}
.modern-grid-item {
	width: 33.33%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 20rpx 0;
	transition: all 0.3s ease;
}
.modern-grid-item:active {
	-webkit-transform: scale(0.95);
	        transform: scale(0.95);
	opacity: 0.8;
}
.modern-grid-icon {
	width: 60rpx;
	height: 60rpx;
	margin-bottom: 10rpx;
	border-radius: 12rpx;
}
.modern-grid-text {
	font-size: 26rpx;
	color: #333;
	font-weight: 500;
}
/* PC端样式适配 - 针对微信小程序PC端 */
/* 当屏幕宽度大于等于768px时应用PC端样式 */
/* PC端页面容器 */
.index-page.pc-mode {
	background-color: #f5f7fa;
	min-height: 100vh;
}
.index-page.pc-mode .pc-content {
	max-width: 1000px;
	margin: 0 auto;
	padding: 0;
	background-color: transparent;
}
/* PC端顶部标题栏优化 */
.index-page.pc-mode .back-container .content text {
	font-size: 18px !important;
	font-weight: 500;
}
/* PC端模式样式 - 通过类名控制 */
.index-page.pc-mode .cu-bar.search {
	max-width: 1000px;
	margin: 20px auto;
	padding: 16px 24px;
	min-height: auto;
	background: white;
	border-radius: 12px;
	box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.index-page.pc-mode .cu-bar.search .action {
	flex: 1;
	font-size: 14px !important;
	padding: 12px 16px;
	border-radius: 8px;
	background-color: #f8f9fa;
	margin: 0 8px;
	transition: all 0.3s ease;
	display: flex;
	align-items: center;
	justify-content: center;
	min-height: 44px;
}
.index-page.pc-mode .cu-bar.search .action:hover {
	background-color: #e9ecef;
	-webkit-transform: translateY(-1px);
	        transform: translateY(-1px);
}
.index-page.pc-mode .swiper {
	max-width: 1000px;
	margin: 20px auto;
	border-radius: 12px;
	overflow: hidden;
	box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
	height: 240px;
}
.index-page.pc-mode .swiper-image {
	height: 240px;
	object-fit: cover;
	width: 100%;
}
.index-page.pc-mode .exam-countdown {
	max-width: 1000px;
	margin: 20px auto;
	padding: 0;
	border-radius: 12px;
}
.index-page.pc-mode .countdown-content {
	border-radius: 12px;
	box-shadow: 0 2px 12px rgba(0, 100, 255, 0.15);
	background: white;
}
.index-page.pc-mode .countdown-header {
	padding: 16px 24px;
	background: linear-gradient(135deg, #1976d2, #42a5f5);
}
.index-page.pc-mode .countdown-title {
	font-size: 16px;
	margin-left: 8px;
	font-weight: 500;
}
.index-page.pc-mode .countdown-info {
	padding: 20px 24px;
}
.index-page.pc-mode .countdown-date {
	font-size: 14px;
	color: #666;
}
.index-page.pc-mode .countdown-days {
	font-size: 24px;
	padding: 8px 16px;
	border-radius: 8px;
	margin-right: 8px;
	font-weight: 600;
}
.index-page.pc-mode .countdown-unit {
	font-size: 14px;
	color: #666;
}
.index-page.pc-mode .cu-bar {
	max-width: 1000px;
	margin: 20px auto;
	padding: 16px 24px;
	min-height: auto;
	background: white;
	border-radius: 12px;
	box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}
.index-page.pc-mode .cu-bar .text-xl {
	font-size: 18px !important;
	font-weight: 600;
}
.index-page.pc-mode .cu-bar .text-ABC {
	font-size: 12px !important;
	color: #666;
}
.index-page.pc-mode .modern-grid {
	max-width: 1000px;
	margin: 20px auto;
	padding: 24px;
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	gap: 16px;
	justify-content: center;
	background: white;
	border-radius: 12px;
	box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}
.index-page.pc-mode .modern-grid-item {
	width: auto;
	padding: 20px 16px;
	border-radius: 12px;
	background-color: #f8f9fa;
	transition: all 0.3s ease;
	cursor: pointer;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	min-height: 120px;
	border: 1px solid #e9ecef;
}
.index-page.pc-mode .modern-grid-item:hover {
	-webkit-transform: translateY(-4px);
	        transform: translateY(-4px);
	box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
	background-color: white;
	border-color: #1976d2;
}
.index-page.pc-mode .modern-grid-icon {
	width: 48px;
	height: 48px;
	margin-bottom: 12px;
	border-radius: 8px;
	transition: all 0.3s ease;
}
.index-page.pc-mode .modern-grid-item:hover .modern-grid-icon {
	-webkit-transform: scale(1.1);
	        transform: scale(1.1);
}
.index-page.pc-mode .modern-grid-text {
	font-size: 14px;
	font-weight: 500;
	text-align: center;
	color: #333;
}
.index-page.pc-mode .modern-grid-item:hover .modern-grid-text {
	color: #1976d2;
}
.index-page.pc-mode .cu-card.article {
	max-width: 1000px;
	margin: 20px auto;
	padding: 0;
	background: white;
	border-radius: 12px;
	box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}
.index-page.pc-mode .cu-card.article .cu-item {
	margin-bottom: 0;
	border-radius: 0;
	transition: all 0.3s ease;
	cursor: pointer;
	border-bottom: 1px solid #f0f0f0;
}
.index-page.pc-mode .cu-card.article .cu-item:last-child {
	border-bottom: none;
	border-radius: 0 0 12px 12px;
}
.index-page.pc-mode .cu-card.article .cu-item:first-child {
	border-radius: 12px 12px 0 0;
}
.index-page.pc-mode .cu-card.article .cu-item:hover {
	-webkit-transform: translateY(-2px);
	        transform: translateY(-2px);
	box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
	background: #f8f9fa;
}
.index-page.pc-mode .cu-card.article .content {
	padding: 20px 24px;
}
.index-page.pc-mode .cu-card.article .content image {
	width: 64px;
	height: 64px;
	border-radius: 8px;
}
.index-page.pc-mode .cu-card.article .desc {
	margin-left: 16px;
	flex: 1;
}
.index-page.pc-mode .cu-card.article .text-df {
	font-size: 16px;
	line-height: 1.5;
	margin-bottom: 8px;
	font-weight: 500;
	color: #333;
}
.index-page.pc-mode .cu-card.article .text-sm {
	font-size: 14px;
	color: #666;
}
.index-page.pc-mode .cu-card.article .action {
	padding: 20px 24px;
	display: flex;
	align-items: center;
	gap: 12px;
}
.index-page.pc-mode .cu-card.article .action .cu-btn {
	font-size: 14px;
	padding: 8px 16px;
	min-height: auto;
	border-radius: 6px;
	transition: all 0.3s ease;
}
.index-page.pc-mode .cu-card.article .action .cu-btn:hover {
	-webkit-transform: translateY(-1px);
	        transform: translateY(-1px);
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}
/* 响应式断点样式 */
@media (min-width: 768px) {
	/* 页面整体布局 */
page {
		background-color: #f5f7fa;
}

	/* 主容器适配 */
.pc-page {
		max-width: 1200px;
		margin: 0 auto;
		background-color: #f5f7fa;
		min-height: 100vh;
		padding: 0 20px;
}
.pc-content {
		max-width: 1000px;
		margin: 0 auto;
		padding: 0;
}

	/* 顶部标题栏PC端适配 */
.cu-custom {
		max-width: 1200px;
		margin: 0 auto;
}
.back-container {
		height: 64px !important;
		max-width: 1200px;
		margin: 0 auto;
}
.back-container .back-bar {
		min-height: 64px !important;
		padding: 0 20px;
}
.back-container .content text {
		font-size: 18px !important;
		font-weight: 500;
}

	/* 搜索栏PC端适配 */
.cu-bar.search {
		max-width: 1000px;
		margin: 20px auto;
		padding: 16px 24px;
		min-height: auto;
		background: white;
		border-radius: 12px;
		box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
		display: flex;
		justify-content: space-between;
		align-items: center;
}
.cu-bar.search .action {
		flex: 1;
		font-size: 14px !important;
		padding: 12px 16px;
		border-radius: 8px;
		background-color: #f8f9fa;
		margin: 0 8px;
		transition: all 0.3s ease;
		display: flex;
		align-items: center;
		justify-content: center;
		min-height: 44px;
}
.cu-bar.search .action:hover {
		background-color: #e9ecef;
		-webkit-transform: translateY(-1px);
		        transform: translateY(-1px);
}

	/* 轮播图PC端适配 */
.swiper {
		max-width: 1000px;
		margin: 20px auto;
		border-radius: 12px;
		overflow: hidden;
		box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
		height: 240px;
}
.swiper-image {
		height: 240px;
		object-fit: cover;
		width: 100%;
}

	/* 考试倒计时PC端适配 */
.exam-countdown {
		max-width: 1000px;
		margin: 20px auto;
		padding: 0;
		border-radius: 12px;
}
.countdown-content {
		border-radius: 12px;
		box-shadow: 0 2px 12px rgba(0, 100, 255, 0.15);
		background: white;
}
.countdown-header {
		padding: 16px 24px;
		background: linear-gradient(135deg, #1976d2, #42a5f5);
}
.countdown-title {
		font-size: 16px;
		margin-left: 8px;
		font-weight: 500;
}
.countdown-info {
		padding: 20px 24px;
}
.countdown-date {
		font-size: 14px;
		color: #666;
}
.countdown-days {
		font-size: 24px;
		padding: 8px 16px;
		border-radius: 8px;
		margin-right: 8px;
		font-weight: 600;
}
.countdown-unit {
		font-size: 14px;
		color: #666;
}

	/* 学习中心标题栏PC端适配 */
.cu-bar {
		max-width: 1000px;
		margin: 20px auto;
		padding: 16px 24px;
		min-height: auto;
		background: white;
		border-radius: 12px;
		box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

	/* PC端标题字体大小优化 */
.cu-bar .text-xl {
		font-size: 18px !important;
		font-weight: 600;
}
.cu-bar .text-ABC {
		font-size: 12px !important;
		color: #666;
}

	/* 宫格列表PC端适配 */
.modern-grid {
		max-width: 1000px;
		margin: 20px auto;
		padding: 24px;
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		gap: 16px;
		justify-content: center;
		background: white;
		border-radius: 12px;
		box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}
.modern-grid-item {
		width: auto;
		padding: 20px 16px;
		border-radius: 12px;
		background-color: #f8f9fa;
		transition: all 0.3s ease;
		cursor: pointer;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		min-height: 120px;
		border: 1px solid #e9ecef;
}
.modern-grid-item:hover {
		-webkit-transform: translateY(-4px);
		        transform: translateY(-4px);
		box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
		background-color: white;
		border-color: #1976d2;
}
.modern-grid-item:active {
		-webkit-transform: translateY(-2px);
		        transform: translateY(-2px);
}
.modern-grid-icon {
		width: 48px;
		height: 48px;
		margin-bottom: 12px;
		border-radius: 8px;
		transition: all 0.3s ease;
}
.modern-grid-item:hover .modern-grid-icon {
		-webkit-transform: scale(1.1);
		        transform: scale(1.1);
}
.modern-grid-text {
		font-size: 14px;
		font-weight: 500;
		text-align: center;
		color: #333;
}
.modern-grid-item:hover .modern-grid-text {
		color: #1976d2;
}

	/* 响应式调整 */
@media screen and (max-width: 1200px) {
.modern-grid {
			grid-template-columns: repeat(3, 1fr);
}
}
@media screen and (max-width: 900px) {
.modern-grid {
			grid-template-columns: repeat(2, 1fr);
}
}

	/* 资讯卡片PC端适配 */
.cu-card.article {
		max-width: 1000px;
		margin: 20px auto;
		padding: 0;
		background: white;
		border-radius: 12px;
		box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}
.cu-card.article .cu-item {
		margin-bottom: 0;
		border-radius: 0;
		transition: all 0.3s ease;
		cursor: pointer;
		border-bottom: 1px solid #f0f0f0;
}
.cu-card.article .cu-item:last-child {
		border-bottom: none;
		border-radius: 0 0 12px 12px;
}
.cu-card.article .cu-item:first-child {
		border-radius: 12px 12px 0 0;
}
.cu-card.article .cu-item:hover {
		-webkit-transform: translateY(-2px);
		        transform: translateY(-2px);
		box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
		background: #f8f9fa;
}
.cu-card.article .content {
		padding: 20px 24px;
}
.cu-card.article .content image {
		width: 64px;
		height: 64px;
		border-radius: 8px;
}
.cu-card.article .desc {
		margin-left: 16px;
		flex: 1;
}
.cu-card.article .text-df {
		font-size: 16px;
		line-height: 1.5;
		margin-bottom: 8px;
		font-weight: 500;
		color: #333;
}
.cu-card.article .text-sm {
		font-size: 14px;
		color: #666;
}

	/* 右侧按钮区域 */
.cu-card.article .action {
		padding: 20px 24px;
		display: flex;
		align-items: center;
		gap: 12px;
}
.cu-card.article .action .cu-btn {
		font-size: 14px;
		padding: 8px 16px;
		min-height: auto;
		border-radius: 6px;
		transition: all 0.3s ease;
}
.cu-card.article .action .cu-btn:hover {
		-webkit-transform: translateY(-1px);
		        transform: translateY(-1px);
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

	/* 底部导航栏PC端适配 */
.cu-bar.tabbar {
		height: 64px !important;
		padding-bottom: 0 !important;
		max-width: 1200px;
		margin: 0 auto;
}
.cu-bar.tabbar .action {
		font-size: 12px !important;
}
.cu-bar.tabbar .action .cuIcon {
		font-size: 24px !important;
}

	/* 分享和更多按钮优化 */
.cu-btn.bg-blue {
		font-size: 14px;
		padding: 8px 16px;
		border-radius: 6px;
		height: auto;
		min-height: auto;
		transition: all 0.3s ease;
}
.cu-btn.bg-blue:hover {
		-webkit-transform: translateY(-1px);
		        transform: translateY(-1px);
		box-shadow: 0 2px 8px rgba(25, 118, 210, 0.3);
}

	/* PC端按钮文字优化 */
.cu-bar .action .cu-btn {
		font-size: 14px !important;
		padding: 8px 16px !important;
		height: auto !important;
		min-height: auto !important;
		border-radius: 6px !important;
}

	/* 按钮图标优化 */
.cu-bar .action .cu-btn .cuIcon-share,
	.cu-bar .action .cu-btn .cuIcon-moreandroid {
		font-size: 14px !important;
		margin-left: 6px !important;
}
}

