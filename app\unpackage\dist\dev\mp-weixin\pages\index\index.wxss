/* 移动端默认样式 (rpx单位) */
.menu-image {
	width: 56rpx;
	height: 56rpx;
	margin-bottom: 10rpx;
}
/* 移动端顶部搜索栏优化 - 缩小三个选择按钮 */
.cu-bar.search {
	min-height: 70rpx;
	padding: 8rpx 15rpx;
}
.cu-bar.search .action {
	font-size: 22rpx;
	padding: 6rpx 10rpx;
	max-width: 30%;
}
.cu-bar.search .action .cuIcon-location,
.cu-bar.search .action .cuIcon-group,
.cu-bar.search .action .cuIcon-read {
	font-size: 24rpx;
	margin-right: 4rpx;
}
/* 搜索栏动作按钮样式 */
.search-action-mobile {
	font-size: 24rpx;
	max-width: 33.3333%;
	padding: 6rpx 10rpx;
}
.search-action-pc {
	font-size: 14px;
	padding: 8px 12px;
	border-radius: 6px;
	background-color: rgba(255, 255, 255, 0.9);
	margin: 0 5px;
}
/* 移动端最新资讯卡片优化 */
.cu-card.article .cu-item {
	margin: 20rpx;
	border-radius: 16rpx;
}
.cu-card.article .content {
	padding: 24rpx;
}
.cu-card.article .content image {
	width: 120rpx;
	height: 120rpx;
	border-radius: 12rpx;
}
.cu-card.article .desc {
	margin-left: 20rpx;
	flex: 1;
}
.cu-card.article .text-df {
	font-size: 28rpx;
	line-height: 1.4;
	margin-bottom: 12rpx;
}
.cu-card.article .text-sm {
	font-size: 24rpx;
}
/* 移动端标题栏按钮优化 - 确保横向排列 */
.cu-bar .action {
	display: flex;
	flex-direction: row;
	align-items: center;
}
.cu-bar .action .cu-btn {
	font-size: 24rpx;
	padding: 10rpx 16rpx;
	min-height: 56rpx;
	display: flex;
	flex-direction: row;
	align-items: center;
}
.cu-bar .action .cu-btn .cuIcon-share,
.cu-bar .action .cu-btn .cuIcon-moreandroid {
	margin-left: 6rpx;
	font-size: 24rpx;
}
/* 移动端学习中心和最新资讯标题优化 */
.cu-bar .sub-title .text-xl {
	font-size: 32rpx;
}
.cu-bar .sub-title .text-ABC {
	font-size: 22rpx;
}
/* 移动端标题栏高度优化 */
.cu-bar {
	min-height: 80rpx;
	padding: 12rpx 30rpx;
}
/* 考试倒计时样式 */
.exam-countdown {
	padding: 0;
	border-radius: 12rpx;
	margin: 0 20rpx;
	overflow: hidden;
}
.countdown-content {
	background: linear-gradient(135deg, #f0f8ff, #e6f2ff);
	border-radius: 12rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 100, 255, 0.15);
	padding: 0;
	position: relative;
	overflow: hidden;
}
.countdown-header {
	background-color: #0081ff;
	color: #ffffff;
	padding: 16rpx 24rpx;
	display: flex;
	align-items: center;
}
.countdown-title {
	font-size: 28rpx;
	font-weight: 500;
	margin-left: 10rpx;
}
.countdown-info {
	padding: 20rpx 24rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.countdown-date {
	font-size: 26rpx;
	color: #555;
}
.countdown-timer {
	display: flex;
	align-items: center;
}
.countdown-days {
	font-size: 40rpx;
	font-weight: bold;
	color: #0081ff;
	background-color: rgba(0, 129, 255, 0.1);
	padding: 6rpx 16rpx;
	border-radius: 8rpx;
	margin-right: 8rpx;
}
.countdown-unit {
	font-size: 28rpx;
	color: #555;
}
/* 新的宫格列表样式 */
.modern-grid {
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	padding: 20rpx 10rpx;
}
.modern-grid-item {
	width: 33.33%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 20rpx 0;
	transition: all 0.3s ease;
}
.modern-grid-item:active {
	-webkit-transform: scale(0.95);
	        transform: scale(0.95);
	opacity: 0.8;
}
.modern-grid-icon {
	width: 60rpx;
	height: 60rpx;
	margin-bottom: 10rpx;
	border-radius: 12rpx;
}
.modern-grid-text {
	font-size: 26rpx;
	color: #333;
	font-weight: 500;
}
/* PC端样式适配 (px单位) - 针对微信小程序PC端 */
/* 当屏幕宽度大于等于1024px时应用PC端样式 */
/* PC端页面容器 */
.index-page.pc-mode {
	background-color: #f5f7fa;
	min-height: 100vh;
}
.index-page.pc-mode .pc-content {
	max-width: 900px;
	margin: 0 auto;
	padding: 15px;
	background-color: #ffffff;
	box-shadow: 0 0 15px rgba(0, 0, 0, 0.08);
	border-radius: 8px;
	margin-top: 15px;
}
/* PC端顶部标题栏优化 */
.index-page.pc-mode .back-container .content text {
	font-size: 18px !important;
}
/* 响应式断点样式 */
@media (min-width: 1024px) {
	/* 顶部标题栏PC端适配 - 整体缩小 */
.cuIcon-moreandroid {
		margin-left: 6px;
		font-size: 14px;
}
.back-container {
		height: 60px !important;
}
.back-container .back-bar {
		min-height: 60px !important;
		padding: 0 15px;
}
.back-container .content text {
		font-size: 16px !important;
}

	/* 搜索栏PC端适配 - 整体缩小 */
.cu-bar.search {
		max-width: 800px;
		margin: 0 auto;
		padding: 8px 20px;
		min-height: 50px;
}
.cu-bar.search .action {
		font-size: 13px !important;
		padding: 6px 10px;
		border-radius: 4px;
		background-color: rgba(255, 255, 255, 0.9);
		margin: 0 3px;
}

	/* 轮播图PC端适配 - 缩小高度 */
.swiper {
		max-width: 700px;
		margin: 8px auto;
		border-radius: 8px;
		overflow: hidden;
		box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}
.swiper-image {
		height: 200px;
		object-fit: cover;
}

	/* 考试倒计时PC端适配 - 整体缩小 */
.exam-countdown {
		max-width: 700px;
		margin: 10px auto;
		padding: 0 20px;
		border-radius: 6px;
}
.countdown-content {
		border-radius: 6px;
		box-shadow: 0 1px 6px rgba(0, 100, 255, 0.15);
}
.countdown-header {
		padding: 8px 16px;
}
.countdown-title {
		font-size: 14px;
		margin-left: 6px;
}
.countdown-info {
		padding: 12px 16px;
}
.countdown-date {
		font-size: 12px;
}
.countdown-days {
		font-size: 18px;
		padding: 3px 8px;
		border-radius: 4px;
		margin-right: 4px;
}
.countdown-unit {
		font-size: 12px;
}

	/* 学习中心标题栏PC端适配 - 整体缩小 */
.cu-bar {
		max-width: 700px;
		margin: 0 auto;
		padding: 8px 20px;
		min-height: 50px;
}

	/* PC端标题字体大小优化 */
.cu-bar .text-xl {
		font-size: 16px !important;
}
.cu-bar .text-ABC {
		font-size: 10px !important;
}

	/* 宫格列表PC端适配 - 缩小尺寸和间距 */
.modern-grid {
		max-width: 700px;
		margin: 0 auto;
		padding: 15px;
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
		gap: 12px;
		justify-content: center;
}
.modern-grid-item {
		width: auto;
		padding: 12px 10px;
		border-radius: 8px;
		background-color: rgba(255, 255, 255, 0.8);
		transition: all 0.3s ease;
		cursor: pointer;
}
.modern-grid-item:hover {
		-webkit-transform: translateY(-2px);
		        transform: translateY(-2px);
		box-shadow: 0 4px 15px rgba(0, 0, 0, 0.12);
		background-color: rgba(255, 255, 255, 0.95);
}
.modern-grid-item:active {
		-webkit-transform: translateY(-1px);
		        transform: translateY(-1px);
}
.modern-grid-icon {
		width: 36px;
		height: 36px;
		margin-bottom: 6px;
		border-radius: 6px;
}
.modern-grid-text {
		font-size: 12px;
		font-weight: 500;
}

	/* 资讯卡片PC端适配 - 整体缩小 */
.cu-card.article {
		max-width: 700px;
		margin: 0 auto;
		padding: 0 20px;
}
.cu-card.article .cu-item {
		margin-bottom: 10px;
		border-radius: 8px;
		transition: all 0.3s ease;
		cursor: pointer;
}
.cu-card.article .cu-item:hover {
		-webkit-transform: translateY(-1px);
		        transform: translateY(-1px);
		box-shadow: 0 4px 15px rgba(0, 0, 0, 0.12);
}
.cu-card.article .content {
		padding: 12px 16px;
}
.cu-card.article .content image {
		width: 50px;
		height: 50px;
		border-radius: 6px;
}
.cu-card.article .desc {
		margin-left: 12px;
}
.cu-card.article .text-df {
		font-size: 13px;
		line-height: 1.4;
		margin-bottom: 6px;
}
.cu-card.article .text-sm {
		font-size: 11px;
}

	/* 右侧按钮区域缩小 */
.cu-card.article .action {
		padding: 8px 12px;
}
.cu-card.article .action .cu-btn {
		font-size: 12px;
		padding: 4px 8px;
		min-height: 28px;
}

	/* 底部导航栏PC端适配 */
.cu-bar.tabbar {
		height: 60px !important;
		padding-bottom: 0 !important;
}
.cu-bar.tabbar .action {
		font-size: 11px !important;
}
.cu-bar.tabbar .action .cuIcon {
		font-size: 20px !important;
}

	/* 分享和更多按钮优化 - 高度自适应 */
.cu-btn.bg-blue {
		font-size: 14px;
		padding: 4px 8px;
		border-radius: 4px;
		height: auto;
		min-height: auto;
}

	/* PC端按钮文字进一步缩小 */
.cu-bar .action .cu-btn {
		font-size: 14px !important;
		padding: 4px 8px !important;
		height: auto !important;
		min-height: auto !important;
}

	/* 按钮图标也缩小 */
.cu-bar .action .cu-btn .cuIcon-share,
	.cu-bar .action .cu-btn .cuIcon-moreandroid {
		font-size: 10px !important;
		margin-left: 3px !important;
}
}

