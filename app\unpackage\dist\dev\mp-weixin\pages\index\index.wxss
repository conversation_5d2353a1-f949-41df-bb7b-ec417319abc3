/* 移动端默认样式 (rpx单位) */
.menu-image {
	width: 56rpx;
	height: 56rpx;
	margin-bottom: 10rpx;
}
/* 搜索栏动作按钮样式 */
.search-action-mobile {
	font-size: 27rpx;
	max-width: 33.3333%;
}
.search-action-pc {
	font-size: 14px;
	padding: 8px 12px;
	border-radius: 6px;
	background-color: rgba(255, 255, 255, 0.9);
	margin: 0 5px;
}
/* 考试倒计时样式 */
.exam-countdown {
	padding: 0;
	border-radius: 12rpx;
	margin: 0 20rpx;
	overflow: hidden;
}
.countdown-content {
	background: linear-gradient(135deg, #f0f8ff, #e6f2ff);
	border-radius: 12rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 100, 255, 0.15);
	padding: 0;
	position: relative;
	overflow: hidden;
}
.countdown-header {
	background-color: #0081ff;
	color: #ffffff;
	padding: 16rpx 24rpx;
	display: flex;
	align-items: center;
}
.countdown-title {
	font-size: 28rpx;
	font-weight: 500;
	margin-left: 10rpx;
}
.countdown-info {
	padding: 20rpx 24rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.countdown-date {
	font-size: 26rpx;
	color: #555;
}
.countdown-timer {
	display: flex;
	align-items: center;
}
.countdown-days {
	font-size: 40rpx;
	font-weight: bold;
	color: #0081ff;
	background-color: rgba(0, 129, 255, 0.1);
	padding: 6rpx 16rpx;
	border-radius: 8rpx;
	margin-right: 8rpx;
}
.countdown-unit {
	font-size: 28rpx;
	color: #555;
}
/* 新的宫格列表样式 */
.modern-grid {
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	padding: 20rpx 10rpx;
}
.modern-grid-item {
	width: 33.33%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 20rpx 0;
	transition: all 0.3s ease;
}
.modern-grid-item:active {
	-webkit-transform: scale(0.95);
	        transform: scale(0.95);
	opacity: 0.8;
}
.modern-grid-icon {
	width: 60rpx;
	height: 60rpx;
	margin-bottom: 10rpx;
	border-radius: 12rpx;
}
.modern-grid-text {
	font-size: 26rpx;
	color: #333;
	font-weight: 500;
}
/* PC端样式适配 (px单位) - 针对微信小程序PC端 */
/* 当屏幕宽度大于等于1024px时应用PC端样式 */
/* PC端页面容器 */
.index-page.pc-mode {
	background-color: #f5f7fa;
	min-height: 100vh;
}
.index-page.pc-mode .pc-content {
	max-width: 1200px;
	margin: 0 auto;
	padding: 20px;
	background-color: #ffffff;
	box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
	border-radius: 12px;
	margin-top: 20px;
}
/* PC端顶部标题栏优化 */
.index-page.pc-mode .back-container .content text {
	font-size: 18px !important;
}
/* 响应式断点样式 */
@media (min-width: 1024px) {
	/* 顶部标题栏PC端适配 */
.back-container .content text {
		font-size: 18px !important;
}

	/* 搜索栏PC端适配 */
.cu-bar.search {
		max-width: 800px;
		margin: 0 auto;
		padding: 0 20px;
}
.cu-bar.search .action {
		font-size: 14px !important;
		padding: 8px 12px;
		border-radius: 6px;
		background-color: rgba(255, 255, 255, 0.9);
		margin: 0 5px;
}

	/* 轮播图PC端适配 */
.swiper {
		max-width: 800px;
		margin: 10px auto;
		border-radius: 12px;
		overflow: hidden;
		box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}
.swiper-image {
		height: 300px;
		object-fit: cover;
}

	/* 考试倒计时PC端适配 */
.exam-countdown {
		max-width: 800px;
		margin: 15px auto;
		padding: 0 20px;
		border-radius: 8px;
}
.countdown-content {
		border-radius: 8px;
		box-shadow: 0 2px 8px rgba(0, 100, 255, 0.15);
}
.countdown-header {
		padding: 12px 20px;
}
.countdown-title {
		font-size: 16px;
		margin-left: 8px;
}
.countdown-info {
		padding: 16px 20px;
}
.countdown-date {
		font-size: 14px;
}
.countdown-days {
		font-size: 24px;
		padding: 4px 12px;
		border-radius: 6px;
		margin-right: 6px;
}
.countdown-unit {
		font-size: 16px;
}

	/* 学习中心标题栏PC端适配 */
.cu-bar {
		max-width: 800px;
		margin: 0 auto;
		padding: 0 20px;
}

	/* PC端标题字体大小优化 */
.cu-bar .text-xl {
		font-size: 20px !important;
}
.cu-bar .text-ABC {
		font-size: 12px !important;
}

	/* 宫格列表PC端适配 */
.modern-grid {
		max-width: 800px;
		margin: 0 auto;
		padding: 20px;
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
		gap: 20px;
		justify-content: center;
}
.modern-grid-item {
		width: auto;
		padding: 20px 15px;
		border-radius: 12px;
		background-color: rgba(255, 255, 255, 0.8);
		transition: all 0.3s ease;
		cursor: pointer;
}
.modern-grid-item:hover {
		-webkit-transform: translateY(-3px);
		        transform: translateY(-3px);
		box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
		background-color: rgba(255, 255, 255, 0.95);
}
.modern-grid-item:active {
		-webkit-transform: translateY(-1px);
		        transform: translateY(-1px);
}
.modern-grid-icon {
		width: 48px;
		height: 48px;
		margin-bottom: 8px;
		border-radius: 8px;
}
.modern-grid-text {
		font-size: 14px;
		font-weight: 500;
}

	/* 资讯卡片PC端适配 */
.cu-card.article {
		max-width: 800px;
		margin: 0 auto;
		padding: 0 20px;
}
.cu-card.article .cu-item {
		margin-bottom: 15px;
		border-radius: 12px;
		transition: all 0.3s ease;
		cursor: pointer;
}
.cu-card.article .cu-item:hover {
		-webkit-transform: translateY(-2px);
		        transform: translateY(-2px);
		box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}
.cu-card.article .content {
		padding: 20px;
}
.cu-card.article .content image {
		width: 70px;
		height: 70px;
		border-radius: 8px;
}
.cu-card.article .desc {
		margin-left: 15px;
}
.cu-card.article .text-df {
		font-size: 14px;
		line-height: 1.5;
		margin-bottom: 10px;
}
.cu-card.article .text-sm {
		font-size: 12px;
}
}

