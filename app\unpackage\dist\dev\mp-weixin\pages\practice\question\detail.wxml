<block wx:if="{{isLoad}}"><view class="{{[pageClasses]}}"><back vue-id="ec8a0ab2-1" showBackText="{{false}}" customClass="bg-gradual-blue text-white" title="题目详情" bind:__l="__l"></back><view><scroll-view style="{{('height:calc(100vh - '+CustomBar+'px - 70px)')}}" scrollY="{{true}}"><view data-event-opts="{{[['tap',[['onCourseTap',['$event']]]]]}}" class="bg-white questionAsk-layout" style="padding-bottom:0;" bindtap="__e"><view class="cu-capsule"><view class="cu-tag bg-blue">题目来源</view><view class="cu-tag line-blue">{{item.course_name}}</view></view></view><block wx:if="{{item.background_id}}"><view><view class="questionAsk-layout"><rich-text nodes="{{item.background_name}}" preview-img="true"></rich-text></view></view></block><view class="questionAsk-layout"><view><rich-text nodes="{{item.questionAsk}}" preview-img="true"></rich-text></view></view><block wx:if="{{item.question_type==1||item.question_type==2||item.question_type==3}}"><view class="options-layout margin-top-xs"><block wx:if="{{item.A}}"><view class="{{[(item.correct_A==1?'layout-result-correct':'')+' layout-result']}}" data-answer="A"><text>A</text><rich-text nodes="{{item.A}}"></rich-text></view></block><block wx:if="{{item.B}}"><view class="{{[(item.correct_B==1?'layout-result-correct':'')+' layout-result']}}" data-answer="B"><text>B</text><rich-text nodes="{{item.B}}"></rich-text></view></block><block wx:if="{{item.C}}"><view class="{{[(item.correct_C==1?'layout-result-correct':'')+' layout-result']}}" data-answer="C"><text>C</text><rich-text nodes="{{item.C}}"></rich-text></view></block><block wx:if="{{item.D}}"><view class="{{[(item.correct_D==1?'layout-result-correct':'')+' layout-result']}}" data-answer="D"><text>D</text><rich-text nodes="{{item.D}}"></rich-text></view></block><block wx:if="{{item.E}}"><view class="{{[(item.correct_E==1?'layout-result-correct':'')+' layout-result']}}" data-answer="E"><text>E</text><rich-text nodes="{{item.E}}"></rich-text></view></block><block wx:if="{{item.F}}"><view class="{{[(item.correct_F==1?'layout-result-correct':'')+' layout-result']}}" data-answer="F"><text>F</text><rich-text nodes="{{item.F}}"></rich-text></view></block></view></block><view class="margin-top-xs"><view class="explain-layout"><view class="explain-label">答案</view><view style="display:flex;"><view class="explain-answer"><block wx:if="{{item.question_type==1||item.question_type==2||item.question_type==3}}"><view>参考答案</view></block><rich-text class="{{[item.question_type==1||item.question_type==2||item.question_type==3?'correct':'answer']}}" nodes="{{item.correctOption}}"></rich-text></view></view><block wx:if="{{item.explanation}}"><view class="explain-label" style="margin-top:40rpx;padding-bottom:30rpx;">解析</view></block><block wx:if="{{item.explanation}}"><rich-text class="explain-text" nodes="{{item.explanation}}" preview-img="true"></rich-text></block></view><view><advideo vue-id="ec8a0ab2-2" unitId="adunit-9099eb3969908c24" adTheme="blue" bind:__l="__l"></advideo></view></view></scroll-view></view><view class="bottom-layout cu-bar border tabbar bg-white"><view data-event-opts="{{[['tap',[['onCollectTap',['$event']]]]]}}" class="action text-blue" bindtap="__e"><view class="cuIcon-likefill"></view>{{''+(item.isCollection==1?'已收藏':'未收藏')+''}}</view><block wx:if="{{!appIsAudit}}"><view class="action text-blue" data-type="1" data-event-opts="{{[['tap',[['onCommentTap',['$event']]]]]}}" bindtap="__e"><view class="cuIcon-commentfill"></view>评论</view></block><view class="action text-blue" data-type="2" data-event-opts="{{[['tap',[['onFeedbackTap',['$event']]]]]}}" bindtap="__e"><view class="cuIcon-warnfill"></view>纠错</view><button class="action text-blue" open-type="share"><view class="cuIcon-forwardfill"></view>转发</button></view></view></block>