rich-text {
	line-height: 40rpx;
}

/* 暗黑模式变量 */
.dark-mode {
    --bg-color: #121212;
    --text-color: #e0e0e0;
    --card-bg: #1e1e1e;
    --border-color: #333333;
    --highlight-color: #4c9eff;
    --secondary-bg: #2c2c2c;
    --secondary-text: #aaaaaa;
    --disabled-color: #777777;
    --shadow-color: rgba(0, 0, 0, 0.3);
}

/* 应用暗黑模式 */
.app-container {
    min-height: 100vh;
}

.app-container.dark-mode {
    background-color: var(--bg-color);
    color: var(--text-color);
}

.app-container.dark-mode .nav {
    background-color: var(--card-bg);
    border-bottom: 1rpx solid var(--border-color);
}

.app-container.dark-mode .nav .cu-item {
    color: var(--text-color);
}

.app-container.dark-mode .nav .cu-item.cur {
    color: var(--highlight-color);
}

.app-container.dark-mode .questionAsk-layout {
    background-color: var(--card-bg);
}

.app-container.dark-mode .question-content {
    background-color: var(--card-bg);
    color: var(--text-color);
}

.app-container.dark-mode .options-layout .layout-result {
    background-color: var(--secondary-bg);
    color: var(--text-color);
    border: 1rpx solid var(--border-color);
}

/* 夜间模式下选项状态样�?*/
.app-container.dark-mode .layout-result-correct {
    background-color: #2c6e45 !important; /* 深绿�?*/
    color: #ffffff !important;
    border: 1px solid #3a8c59 !important;
}

.app-container.dark-mode .layout-result-error {
    background-color: #8c3a3a !important; /* 深红�?*/
    color: #ffffff !important;
    border: 1px solid #a14747 !important;
}

.app-container.dark-mode .layout-result-select {
    background-color: #324b7a !important; /* 深蓝�?*/
    color: #ffffff !important;
    border: 1px solid #3e5c94 !important;
}

.app-container.dark-mode .bottom-layout {
    background-color: var(--card-bg);
    box-shadow: 0 -2rpx 6rpx var(--shadow-color);
    border-top: 1rpx solid var(--border-color);
}

/* 底部导航栏样�?*/
.bottom-layout {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 99;
    background-color: #ffffff;
    box-shadow: 0 -2rpx 6rpx rgba(0, 0, 0, 0.1);
    border-top: 1rpx solid #eee;
}

.bottom-layout .action {
    color: #333333; /* 普通按钮颜�?*/
}

.bottom-layout .action.text-blue {
    color: #0081ff; /* 蓝色按钮颜色 */
}

/* PC端响应式样式 */
@media screen and (min-width: 768px) {
    /* 整体容器PC端适配 */
    .app-container {
        max-width: 900px;
        margin: 0 auto;
        background-color: #ffffff;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.08);
        border-radius: 8px;
        margin-top: 15px;
        min-height: calc(100vh - 30px);
    }

    /* 顶部导航栏PC端适配 */
    .nav {
        max-width: 900px;
        margin: 0 auto;
        border-radius: 8px 8px 0 0;
        overflow: hidden;
    }

    .nav .cu-item {
        font-size: 14px;
        padding: 8px 16px;
    }

    /* 题目内容区域PC端适配 */
    .questionAsk-layout {
        max-width: 800px;
        margin: 0 auto;
        padding: 15px 20px;
        border-radius: 8px;
    }

    .question-content {
        padding: 15px 20px;
        font-size: 14px;
        line-height: 1.6;
    }

    /* 选项布局PC端适配 */
    .options-layout {
        max-width: 800px;
        margin: 0 auto;
        padding: 0 20px;
    }

    .options-layout .layout-result {
        margin: 8px 0;
        padding: 12px 16px;
        border-radius: 6px;
        font-size: 14px;
    }

    /* 底部操作栏PC端适配 */
    .bottom-layout {
        max-width: 900px;
        margin: 0 auto;
        left: 50%;
        transform: translateX(-50%);
        border-radius: 0 0 8px 8px;
        position: relative;
        margin-top: 20px;
    }

    .bottom-layout .action {
        font-size: 14px;
        padding: 8px 16px;
    }

    /* 题目标签PC端适配 */
    .cu-capsule .cu-tag {
        font-size: 12px;
        padding: 4px 8px;
    }

    /* 富文本内容PC端适配 */
    rich-text {
        line-height: 1.6;
        font-size: 14px;
    }
}

.app-container.dark-mode .bottom-layout .action {
    color: var(--text-color);
}

.app-container.dark-mode .bottom-layout .action.text-blue {
    color: var(--highlight-color);
}

.app-container.dark-mode .cu-btn.shadow {
    background-color: #444444;
}

.cu-btn.shadow {
    background-color: #0081ff; /* 蓝色按钮 */
}

.app-container.dark-mode .display-card {
    background-color: var(--card-bg);
    border: 1rpx solid var(--border-color);
}

.app-container.dark-mode .display-card-header {
    border-bottom: 1rpx solid var(--border-color);
}

.app-container.dark-mode .operation-btn {
    background-color: #2d2d2d;
    color: var(--text-color);
    border: 1px solid #444444;
}

.app-container.dark-mode .operation-text {
    color: var(--text-color);
    font-weight: 500;
}

.app-container.dark-mode .operation-icon {
    color: var(--highlight-color);
}

.app-container.dark-mode .cu-dialog {
    background-color: var(--card-bg);
    color: var(--text-color);
}

.app-container.dark-mode .cu-bar.bg-white {
    background-color: var(--card-bg);
    color: var(--text-color);
}

/* 夜间模式下答题卡样式 */
.app-container.dark-mode .cu-dialog .bg-white {
    background-color: var(--card-bg) !important;
    color: var(--text-color);
}

.app-container.dark-mode .text-green {
    color: #4cd964 !important;
}

.app-container.dark-mode .text-red {
    color: #ff6b6b !important;
}

.app-container.dark-mode .text-gray {
    color: #aaaaaa !important;
}

/* 夜间模式下表单组样式 */
.app-container.dark-mode .cu-form-group {
    background-color: var(--card-bg);
    color: var(--text-color);
    border-top: 1px solid var(--border-color);
    border-bottom: 1px solid var(--border-color);
}

.app-container.dark-mode .cu-form-group .title {
    color: var(--text-color);
}

/* 夜间模式下开关样�?*/
.app-container.dark-mode switch {
    background-color: #444 !important;
}

.app-container.dark-mode switch.checked {
    background-color: var(--highlight-color) !important;
}

.questionAsk-layout {
	display: flex;
	flex-direction: column;
}

.questionAsk-layout .nosupport {
	color: #999;
	margin-top: 30rpx;
}

.options-layout {
	display: flex;
	flex-direction: column;
	background: white;
	padding-bottom: 30rpx;
}

.bottom-layout {
	position: fixed;
	left: 0;
	right: 0;
	bottom: 0;
}

.options-layout rich-text {
	margin-left: 30rpx;
}

.layout-result {
	margin: 30rpx 30rpx 0 30rpx;
	padding: 30rpx 30rpx 30rpx 40rpx;
	display: flex;
	align-items: center;
	background: #f5f5f5;
	color: #333;
	border-radius: 20rpx;
}

.layout-result-correct {
	display: flex;
	align-items: center;
	background: #62d88b;
	color: white;
}

.layout-result-error {
	display: flex;
	align-items: center;
	background: #fd7d7f;
	color: white;
}

.layout-result-select {
	display: flex;
	align-items: center;
	background: rgb(127, 168, 243);
	color: white;
}

.text-submit {
	text-align: center;
	border-radius: 20rpx;
	margin-top: 30rpx;
	margin-left: 30%;
	margin-right: 30%;
}

.explain-layout {
	padding: 30rpx;
	padding-bottom: 80rpx; /* 增加底部边距，避免被凸起的保存按钮遮�?*/
}


.explain-answer {
	display: flex;
	flex-direction: row;
	align-items: center;
}

.explain-answer .correct {
	margin-left: 30rpx;
	color: #62d88b;
	font-weight: bold;
}

.explain-answer .error {
	margin-left: 30rpx;
	color: #fd7d7f;
	font-weight: bold;
}

