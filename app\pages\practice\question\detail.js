let app = getApp();
let that = null;
export default {
	data() {
		return {
			id: 0,
			item: [],
			title: '',
			CustomBar: this.CustomBar,
			isLoad: false,
			options: ['A', 'B', 'C', 'D', 'E', 'F'],
			appIsAudit: false,
			currentTopicTypeLabel: '',
			// PC端适配相关数据
			isPC: false,
			screenType: 'mobile'
		};
	},
	computed: {
		pageClasses() {
			return 'detail-page' + (this.isPC ? ' pc-mode' : '');
		}
	},
	onLoad: function(options) {
		that = this;
		that.id = options.id;
		that.appIsAudit = app.globalData.checkAppIsAudit();
		that.detectPCMode();
		that.getQuestion();
	},
	onShow: function(options) {
		app.globalData.showShareMenu();
	},
	onShareAppMessage: function() {
		let item = that.item;
		let config = app.globalData.getShareConfig();
		config.title = item.questionAsk;
		config.path = '/pages/practice/question/detail?id=' + item.id;
		console.log(config);
		return config;
	},
	methods: {
		getQuestion: function() {
			app.globalData.server
				.getRequest('question/getInfo', {
					id: that.id
				})
				.then(function(res) {
					console.log(res);
					let item = res.data;
					if (item.question_type <= 3) {
						for (let key in that.options) {
							let opKey = that.options[key];
							let opCorrectKey = 'correct_' + opKey;
							if (item.hasOwnProperty(opKey) && item.correctOption.indexOf(opKey) != -1) {
								item[opCorrectKey] = 1;
							} else {
								item[opCorrectKey] = 0;
							}
						}
					}
					that.item = item;
					that.title = item.questionAsk;
					that.isLoad = true;
					that.currentTopicTypeLabel = app.globalData.practice.getTopicTypeLabel(res.data.question_type);
				})
				.catch(function(res) {
					console.log(res);
					app.showToast('获取答题记录失败');
				});
		},

		onCollectTap: function() {
			let item = that.item;
			if (item.isCollection == 1) {
				item.isCollection = 2;
			} else {
				item.isCollection = 1;
			}
			app.globalData.server
				.postRequest('question/collect', {
					id: item.id,
					type: item.isCollection
				})
				.then(function(e) {
					app.showToast('收藏成功');
				})
				.catch(function(a) {
					app.showToast('收藏出错');
				});
		},
		onCommentTap: function() {
			app.showToast('敬请期待');
		},
		onCourseTap: function() {
			let item = that.item;
			uni.navigateTo({
				url: '/pages/practice/course/detail?id=' + item.course_id
			});
		},
		onFeedbackTap: function() {
			let item = that.item;
			uni.navigateTo({
				url: '/pages/practice/feedback/feedback?id=' + item.id
			});
		},

		/**
		 * 检测PC端模式
		 */
		detectPCMode: function() {
			try {
				// 获取系统信息
				const systemInfo = uni.getSystemInfoSync();
				const windowWidth = systemInfo.windowWidth;

				// 简单判断：宽度大于768认为是PC端
				that.isPC = windowWidth >= 768;
				that.screenType = windowWidth >= 768 ? 'desktop' : 'mobile';

				console.log('题目详情页PC端检测结果:', {
					windowWidth: windowWidth,
					isPC: that.isPC,
					screenType: that.screenType
				});
			} catch (error) {
				console.error('PC端检测失败:', error);
				that.isPC = false;
				that.screenType = 'mobile';
			}
		}
	}
};