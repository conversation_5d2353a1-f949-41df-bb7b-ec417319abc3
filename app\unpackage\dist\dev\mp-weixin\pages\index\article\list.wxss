/* PC端响应式样式 */
@media screen and (min-width: 768px) {
    /* 整体容器PC端适配 */
.list-container {
        max-width: 900px;
        margin: 15px auto;
        background-color: #ffffff;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.08);
        border-radius: 8px;
        min-height: calc(100vh - 30px);
        overflow: hidden;
}

    /* 分类导航PC端适配 */
.nav {
        padding: 8px 20px;
        border-radius: 8px 8px 0 0;
}
.nav .cu-item {
        font-size: 14px;
        padding: 8px 16px;
}

    /* 滚动区域PC端适配 */
.scroll-Y {
        padding: 0 20px;
}

    /* 文章卡片PC端适配 */
.cu-card.article .cu-item {
        margin: 10px 0;
        border-radius: 8px;
        transition: all 0.3s ease;
        cursor: pointer;
}
.cu-card.article .cu-item:hover {
        -webkit-transform: translateY(-1px);
                transform: translateY(-1px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.12);
}
.cu-card.article .content {
        padding: 12px 16px;
}
.cu-card.article .content image {
        width: 50px;
        height: 50px;
        border-radius: 6px;
}
.cu-card.article .desc {
        margin-left: 12px;
}
.cu-card.article .text-df {
        font-size: 13px;
        line-height: 1.4;
        margin-bottom: 6px;
}
.cu-card.article .text-sm {
        font-size: 11px;
}
}
