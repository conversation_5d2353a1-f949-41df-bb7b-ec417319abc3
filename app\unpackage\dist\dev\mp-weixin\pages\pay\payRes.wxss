/* 支付成功页面样式 */
.success-container {
  padding: 40rpx 30rpx;
  border-radius: 20rpx;
  margin: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}
.success-icon-box {
  text-align: center;
  margin-bottom: 20rpx;
}
.success-icon {
  font-size: 120rpx;
  -webkit-animation: pulse 1.5s ease-in-out;
          animation: pulse 1.5s ease-in-out;
}
.order-card {
  margin: 30rpx;
  border-radius: 20rpx;
  overflow: hidden;
}
/* 动画效果 */
@-webkit-keyframes pulse {
0% {
    -webkit-transform: scale(0.5);
            transform: scale(0.5);
    opacity: 0;
}
50% {
    -webkit-transform: scale(1.2);
            transform: scale(1.2);
}
100% {
    -webkit-transform: scale(1);
            transform: scale(1);
    opacity: 1;
}
}
@keyframes pulse {
0% {
    -webkit-transform: scale(0.5);
            transform: scale(0.5);
    opacity: 0;
}
50% {
    -webkit-transform: scale(1.2);
            transform: scale(1.2);
}
100% {
    -webkit-transform: scale(1);
            transform: scale(1);
    opacity: 1;
}
}
/* PC端响应式样式 */
@media screen and (min-width: 768px) {
  /* 支付成功页面容器PC端适配 */
.success-container {
    max-width: 900px;
    margin: 15px auto;
    padding: 30px 24px;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}
.success-icon-box {
    margin-bottom: 16px;
}
.success-icon {
    font-size: 80px;
}

  /* 订单卡片PC端适配 */
.order-card {
    max-width: 900px;
    margin: 20px auto;
    border-radius: 12px;
}

  /* 按钮PC端适配 */
.cu-btn {
    font-size: 14px;
    padding: 10px 20px;
    border-radius: 6px;
    min-height: 40px;
}

  /* 文字大小PC端适配 */
.text-xl {
    font-size: 18px !important;
}
.text-lg {
    font-size: 16px !important;
}
.text-df {
    font-size: 14px !important;
}
.text-sm {
    font-size: 12px !important;
}
}
