page {
	background: #f2f2f2;
}

.UCenter-bg {
	height: 550rpx;
	display: flex;
	justify-content: center;
	padding-top: 40rpx;
	overflow: hidden;
	position: relative;
	flex-direction: column;
	align-items: center;
	color: #fff;
	font-weight: 300;
	text-shadow: 0 0 3px rgba(0, 0, 0, 0.3);
}

.UCenter-bg image {
	width: 200rpx;
	height: 200rpx;
}

.UCenter-bg .gif-wave {
	position: absolute;
	width: 100%;
	bottom: 0;
	left: 0;
	z-index: 99;
	mix-blend-mode: screen;
	height: 100rpx;
}

.menu-image {
	position: relative;
	display: block;
	margin-top: 12rpx;
	width: 52rpx;
	height: 52rpx;
}

button::after {
	border: none;
}

.cu-list.grid {
	padding: 20rpx 10rpx;
}

.cu-list.grid>.cu-item {
	padding: 20rpx 0;
}

.cu-list.grid>.cu-item text {
	font-size: 28rpx;
	margin-top: 10rpx;
}

.text-xxl {
	font-size: 46rpx !important;
}

.margin-top-sm {
	font-size: 26rpx;
}

/* PC端响应式样式 */
@media screen and (min-width: 768px) {
    /* 整体页面PC端适配 */
    page {
        background: #f5f5f5;
    }

    /* 用户头像背景区域PC端适配 */
    .UCenter-bg {
        max-width: 900px;
        margin: 15px auto;
        height: 300px;
        border-radius: 8px 8px 0 0;
        padding-top: 30px;
    }

    .UCenter-bg .cu-avatar {
        width: 80px;
        height: 80px;
    }

    .UCenter-bg .text-xl {
        font-size: 18px;
        margin-top: 12px;
    }

    .UCenter-bg .sign {
        font-size: 14px;
    }

    .UCenter-bg .gif-wave {
        height: 60px;
    }

    /* 用户信息卡片PC端适配 */
    .padding.flex.text-center.text-grey.bg-white.shadow-warp {
        max-width: 900px;
        margin: 0 auto;
        padding: 20px;
    }

    .text-xxl {
        font-size: 20px !important;
    }

    .margin-top-sm {
        font-size: 12px !important;
        margin-top: 8px;
    }

    /* 标题栏PC端适配 */
    .cu-bar {
        max-width: 900px;
        margin: 0 auto;
        padding: 8px 20px;
        min-height: 50px;
    }

    .cu-bar .action {
        font-size: 14px;
    }

    /* 菜单网格PC端适配 */
    .cu-list.grid {
        max-width: 900px;
        margin: 0 auto;
        padding: 15px 20px;
        background-color: #ffffff;
        border-radius: 0 0 8px 8px;
    }

    .cu-list.grid .cu-item {
        padding: 15px 0;
        transition: all 0.3s ease;
    }

    .cu-list.grid .cu-item .menu-image {
        width: 36px;
        height: 36px;
        margin-top: 8px;
    }

    .cu-list.grid .cu-item text {
        font-size: 12px !important;
        margin-top: 8px;
    }

    .cu-list.grid .cu-item:hover {
        transform: translateY(-3px);
    }
}