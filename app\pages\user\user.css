page {
	background: #f2f2f2;
}

.UCenter-bg {
	height: 550rpx;
	display: flex;
	justify-content: center;
	padding-top: 40rpx;
	overflow: hidden;
	position: relative;
	flex-direction: column;
	align-items: center;
	color: #fff;
	font-weight: 300;
	text-shadow: 0 0 3px rgba(0, 0, 0, 0.3);
}

.UCenter-bg image {
	width: 200rpx;
	height: 200rpx;
}

.UCenter-bg .gif-wave {
	position: absolute;
	width: 100%;
	bottom: 0;
	left: 0;
	z-index: 99;
	mix-blend-mode: screen;
	height: 100rpx;
}

.menu-image {
	position: relative;
	display: block;
	margin-top: 12rpx;
	width: 52rpx;
	height: 52rpx;
}

button::after {
	border: none;
}

.cu-list.grid {
	padding: 20rpx 10rpx;
}

.cu-list.grid>.cu-item {
	padding: 20rpx 0;
}

.cu-list.grid>.cu-item text {
	font-size: 28rpx;
	margin-top: 10rpx;
}

.text-xxl {
	font-size: 46rpx !important;
}

.margin-top-sm {
	font-size: 26rpx;
}

/* 引入统一响应式适配 */
@import url("../../common/css/responsive.css");

/* 用户页面特定的PC端适配 - 基于统一标准 */
@media (min-width: 768px) {
    /* 用户头像背景区域PC端适配 - 对标首页1000px标准 */
    .UCenter-bg {
        max-width: 1000px;
        margin: 20px auto;
        height: 300px;
        border-radius: 12px 12px 0 0;
        padding-top: 30px;
    }

    .UCenter-bg .cu-avatar {
        width: 80px;
        height: 80px;
    }

    .UCenter-bg .text-xl {
        font-size: 18px;
        margin-top: 12px;
    }

    .UCenter-bg .sign {
        font-size: 14px;
    }

    .UCenter-bg .gif-wave {
        height: 60px;
    }

    /* 用户信息卡片PC端适配 - 对标首页1000px标准 */
    .padding.flex.text-center.text-grey.bg-white.shadow-warp {
        max-width: 1000px;
        margin: 0 auto;
        padding: 20px;
        border-radius: 0 0 12px 12px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    }

    .text-xxl {
        font-size: 20px !important;
    }

    .margin-top-sm {
        font-size: 12px !important;
        margin-top: 8px;
    }

    /* 功能网格PC端适配 - 对标首页1000px标准 */
    .cu-list.grid {
        max-width: 1000px;
        margin: 20px auto;
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        padding: 20px;
    }

    .cu-list.grid .cu-item {
        padding: 15px 0;
        transition: all 0.3s ease;
    }

    .cu-list.grid .cu-item .menu-image {
        width: 36px;
        height: 36px;
        margin-top: 8px;
    }

    .cu-list.grid .cu-item text {
        font-size: 12px !important;
        margin-top: 8px;
    }

    .cu-list.grid .cu-item:hover {
        transform: translateY(-3px);
    }
}