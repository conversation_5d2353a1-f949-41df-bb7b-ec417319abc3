/* PC端响应式样式 */
@media screen and (min-width: 768px) {
    /* 整体页面容器PC端适配 */
    .exam-container {
        max-width: 900px;
        margin: 15px auto;
        background-color: #ffffff;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.08);
        border-radius: 8px;
        min-height: calc(100vh - 30px);
        overflow: hidden;
    }

    /* 考试列表PC端适配 */
    .cu-list.menu {
        padding: 0 20px;
    }

    .cu-list.menu .cu-item {
        padding: 12px 16px;
        font-size: 14px;
        border-radius: 6px;
        margin-bottom: 4px;
        transition: all 0.3s ease;
    }

    .cu-list.menu .cu-item:hover {
        background-color: #f8f9fa;
        transform: translateY(-1px);
    }

    /* 考试卡片PC端适配 */
    .cu-card .cu-item {
        margin: 10px 0;
        border-radius: 8px;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .cu-card .cu-item:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.12);
    }

    /* 标题栏PC端适配 */
    .cu-bar {
        padding: 8px 20px;
        min-height: 50px;
    }

    .cu-bar .action {
        font-size: 14px;
    }

    /* 按钮PC端适配 */
    .cu-btn {
        font-size: 14px;
        padding: 8px 16px;
        border-radius: 6px;
        min-height: 36px;
    }

    /* 文字大小PC端适配 */
    .text-xl {
        font-size: 18px !important;
    }

    .text-lg {
        font-size: 16px !important;
    }

    .text-df {
        font-size: 14px !important;
    }

    .text-sm {
        font-size: 12px !important;
    }
}