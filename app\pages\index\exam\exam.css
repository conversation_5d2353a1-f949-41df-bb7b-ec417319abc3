/* 引入统一响应式适配 */
@import url("../../../common/css/responsive.css");

/* 考试页面特定的PC端适配 - 基于统一标准 */
@media (min-width: 768px) {
    /* 整体页面容器PC端适配 - 对标首页1000px标准 */
    .flex.flex-direction {
        max-width: 1000px;
        margin: 20px auto;
        background-color: #ffffff;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        border-radius: 12px;
        min-height: calc(100vh - 40px);
    }

    /* 网格列表适配 - 对标首页标准 */
    .cu-list.grid.col-3 {
        padding: 20px;
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 16px;
    }

    /* 考试列表PC端适配 - 对标首页标准 */
    .cu-list.menu {
        padding: 0 20px;
    }

    .cu-list.menu .cu-item {
        padding: 12px 16px;
        font-size: 14px;
        border-radius: 6px;
        margin-bottom: 4px;
        transition: all 0.3s ease;
    }

    .cu-list.menu .cu-item:hover {
        background-color: #f8f9fa;
        transform: translateY(-1px);
    }

    /* 考试卡片PC端适配 */
    .cu-card .cu-item {
        margin: 10px 0;
        border-radius: 8px;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .cu-card .cu-item:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.12);
    }

    /* 标题栏PC端适配 */
    .cu-bar {
        padding: 8px 20px;
        min-height: 50px;
    }

    .cu-bar .action {
        font-size: 14px;
    }

    /* 按钮PC端适配 */
    .cu-btn {
        font-size: 14px;
        padding: 8px 16px;
        border-radius: 6px;
        min-height: 36px;
    }

    /* 文字大小PC端适配 */
    .text-xl {
        font-size: 18px !important;
    }

    .text-lg {
        font-size: 16px !important;
    }

    .text-df {
        font-size: 14px !important;
    }

    .text-sm {
        font-size: 12px !important;
    }
}