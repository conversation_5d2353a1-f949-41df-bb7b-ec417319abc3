/* 绑定容器 */
.bind-container {
  padding: 30rpx;
}
/* 顶部说明区域 */
.header-info {
  text-align: center;
  margin-bottom: 50rpx;
  padding: 20rpx 0;
}
.title-text {
  font-size: 36rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
}
.desc-text {
  font-size: 28rpx;
  color: #666;
}
/* 分隔�?*/
.divider {
  display: flex;
  align-items: center;
  margin: 40rpx 0;
}
.divider .line {
  flex: 1;
  height: 1rpx;
  background-color: #eee;
}
.divider .text {
  padding: 0 30rpx;
  font-size: 28rpx;
  color: #999;
}
/* 快捷绑定区域 */
.quick-bind-section, .manual-bind-section {
  margin-bottom: 40rpx;
}
.section-title {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: 500;
}
/* 按钮样式 */
.cu-btn.radius {
  border-radius: 12rpx;
}
.cu-btn.lg {
  height: 90rpx;
  font-size: 32rpx;
}
/* 协议部分 */
.agreement-section {
  margin-top: 30rpx;
  margin-bottom: 40rpx;
}
.agreement-text {
  font-size: 26rpx;
  color: #666;
  margin-left: 10rpx;
}
/* 帮助信息区域 */
.help-section {
  margin-top: 30rpx;
}
/* 表单组样式优�?*/
.cu-form-group {
  background-color: #f8f8f8;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  border: none;
}
.cu-form-group .title {
  font-size: 28rpx;
  color: #333;
}
.cu-form-group input {
  font-size: 28rpx;
}
/* PC端响应式样式 */
@media screen and (min-width: 768px) {
  /* 绑定容器PC端适配 */
.bind-container {
    max-width: 900px;
    margin: 15px auto;
    background-color: #ffffff;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.08);
    border-radius: 8px;
    min-height: calc(100vh - 30px);
    padding: 40px;
}

  /* 顶部说明区域PC端适配 */
.header-info {
    margin-bottom: 30px;
    padding: 16px 0;
}
.title-text {
    font-size: 20px;
    margin-bottom: 12px;
}
.desc-text {
    font-size: 14px;
}

  /* 分隔线PC端适配 */
.divider {
    margin: 24px 0;
}
.divider .text {
    padding: 0 20px;
    font-size: 14px;
}

  /* 快捷绑定区域PC端适配 */
.quick-bind-section, .manual-bind-section {
    margin-bottom: 24px;
}
.section-title {
    font-size: 16px;
    margin-bottom: 12px;
}

  /* 按钮PC端适配 */
.cu-btn.radius {
    border-radius: 6px;
}
.cu-btn.lg {
    height: 48px;
    font-size: 16px;
    min-height: 48px;
}

  /* 协议部分PC端适配 */
.agreement-section {
    margin-top: 20px;
    margin-bottom: 24px;
}
.agreement-text {
    font-size: 12px;
    margin-left: 8px;
}

  /* 帮助信息区域PC端适配 */
.help-section {
    margin-top: 20px;
}

  /* 表单组PC端适配 */
.cu-form-group {
    border-radius: 6px;
    margin-bottom: 12px;
}
.cu-form-group .title {
    font-size: 14px;
}
.cu-form-group input {
    font-size: 14px;
}
}
