# 微信小程序PC端适配方案

## 概述

本项目为微信小程序在PC端运行时提供了专门的px单位样式适配方案，从首页开始实现两套样式系统：
- 移动端：使用rpx单位的响应式设计
- PC端：使用px单位的固定像素设计

## 实现方案

### 1. PC端检测

使用项目中已有的`pc-adapter.js`进行PC端环境检测：
- 检测是否为PC端运行环境
- 检测是否为微信PC端
- 获取屏幕类型和窗口信息

### 2. 样式适配策略

#### 移动端样式（默认）
- 使用rpx单位进行响应式适配
- 保持原有的移动端设计风格
- 适用于手机和小屏设备

#### PC端样式
- 使用px单位进行固定像素设计
- 通过CSS媒体查询`@media (min-width: 1024px)`应用
- 针对大屏幕优化的布局和交互

### 3. 首页适配实现

#### 文件修改清单
1. `pages/index/index.vue` - 添加PC端检测和样式类绑定
2. `pages/index/index.js` - 集成PC适配器逻辑
3. `pages/index/index.css` - 添加PC端专用样式

#### 主要适配内容
- **搜索栏**: PC端使用固定宽度容器，优化按钮样式
- **轮播图**: PC端限制最大宽度，添加圆角和阴影
- **考试倒计时**: PC端优化字体大小和间距
- **宫格列表**: PC端使用Grid布局，添加悬停效果
- **资讯卡片**: PC端优化卡片样式和交互效果

### 4. 技术特点

#### 响应式断点
```css
@media (min-width: 1024px) {
  /* PC端样式 */
}
```

#### 动态样式类
```javascript
:class="['index-page', { 
  'pc-mode': isPC, 
  'pc-weixin': isPCWeixin, 
  [`screen-${screenType}`]: true 
}]"
```

#### 条件渲染
```javascript
// PC端显示测试按钮
<button v-if="isPC" @click="goTo('./pc-test')">PC测试</button>
```

### 5. 测试页面

创建了`pages/index/pc-test.vue`测试页面，用于验证PC端适配效果：
- 显示PC端检测信息
- 演示样式适配效果
- 提供实时的环境信息反馈

### 6. 样式对比

| 元素 | 移动端(rpx) | PC端(px) | 说明 |
|------|-------------|----------|------|
| 搜索栏字体 | 27rpx | 14px | PC端使用固定像素 |
| 轮播图高度 | 自适应 | 300px | PC端固定高度 |
| 宫格图标 | 60rpx | 48px | PC端略小更精致 |
| 卡片圆角 | 12rpx | 8px | PC端使用标准圆角 |
| 容器最大宽度 | 100% | 800px | PC端限制最大宽度 |

### 7. 使用方法

1. 在页面的`onLoad`中调用`initPCAdapter()`
2. 在模板中使用条件样式类和条件渲染
3. 在CSS中添加对应的媒体查询样式

### 8. 扩展建议

- 可以为其他页面应用相同的适配模式
- 考虑添加更多响应式断点（平板等）
- 可以根据实际需求调整PC端的最大宽度和布局

## 总结

这套PC端适配方案实现了：
1. ✅ 自动检测PC端环境
2. ✅ 双套样式系统（rpx + px）
3. ✅ 响应式布局适配
4. ✅ 良好的用户体验
5. ✅ 易于扩展和维护

通过这种方式，微信小程序在PC端运行时能够提供更适合大屏幕的用户界面和交互体验。
