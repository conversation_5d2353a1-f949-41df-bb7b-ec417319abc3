/* 通用PC端响应式样式 */
/* 适用于所有页面的基础响应式布局 */

@media screen and (min-width: 768px) {
    /* 通用页面容器 */
    .page-container {
        max-width: 900px;
        margin: 15px auto;
        background-color: #ffffff;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.08);
        border-radius: 8px;
        min-height: calc(100vh - 30px);
        overflow: hidden;
    }

    /* 通用标题栏 */
    .cu-bar {
        max-width: 900px;
        margin: 0 auto;
        padding: 8px 20px;
        min-height: 50px;
    }

    .cu-bar .action {
        font-size: 14px;
    }

    .cu-bar .action .cu-btn {
        font-size: 14px;
        padding: 4px 8px;
        border-radius: 4px;
        height: auto;
        min-height: auto;
    }

    /* 通用按钮样式 */
    .cu-btn {
        font-size: 14px;
        padding: 8px 16px;
        border-radius: 6px;
        min-height: 40px;
    }

    .cu-btn.lg {
        font-size: 16px;
        padding: 12px 24px;
        min-height: 48px;
    }

    .cu-btn.sm {
        font-size: 12px;
        padding: 6px 12px;
        min-height: 32px;
    }

    /* 通用卡片样式 */
    .cu-card .cu-item {
        border-radius: 8px;
        margin: 10px 0;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .cu-card .cu-item:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.12);
    }

    /* 通用列表样式 */
    .cu-list.menu {
        padding: 0 20px;
    }

    .cu-list.menu .cu-item {
        padding: 12px 16px;
        font-size: 14px;
        border-radius: 6px;
        margin-bottom: 4px;
        transition: all 0.3s ease;
    }

    .cu-list.menu .cu-item:hover {
        background-color: #f8f9fa;
    }

    /* 通用网格样式 */
    .cu-list.grid {
        padding: 15px 20px;
    }

    .cu-list.grid .cu-item {
        padding: 12px 8px;
        transition: all 0.3s ease;
    }

    .cu-list.grid .cu-item:hover {
        transform: translateY(-2px);
    }

    .cu-list.grid .cu-item text {
        font-size: 12px;
    }

    /* 通用表单样式 */
    .cu-form-group {
        margin: 12px 20px;
        border-radius: 6px;
    }

    .cu-form-group input,
    .cu-form-group textarea {
        font-size: 14px;
        padding: 10px 12px;
    }

    /* 通用导航样式 */
    .nav .cu-item {
        font-size: 14px;
        padding: 8px 16px;
    }

    /* 通用内容区域 */
    .content-area {
        padding: 20px;
    }

    /* 通用滚动区域 */
    .scroll-area {
        padding: 0 20px;
    }

    /* 通用图片样式 */
    .responsive-image {
        max-width: 100%;
        height: auto;
        border-radius: 6px;
    }

    /* 通用文字大小 */
    .text-xl {
        font-size: 18px !important;
    }

    .text-lg {
        font-size: 16px !important;
    }

    .text-df {
        font-size: 14px !important;
    }

    .text-sm {
        font-size: 12px !important;
    }

    .text-xs {
        font-size: 10px !important;
    }

    /* 通用间距 */
    .margin-xs {
        margin: 4px;
    }

    .margin-sm {
        margin: 8px;
    }

    .margin {
        margin: 12px;
    }

    .margin-lg {
        margin: 20px;
    }

    .padding-xs {
        padding: 4px;
    }

    .padding-sm {
        padding: 8px;
    }

    .padding {
        padding: 12px;
    }

    .padding-lg {
        padding: 20px;
    }

    /* 通用阴影 */
    .shadow {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .shadow-lg {
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
    }

    /* 通用圆角 */
    .radius {
        border-radius: 6px;
    }

    .radius-lg {
        border-radius: 12px;
    }

    /* 通用动画 */
    .transition {
        transition: all 0.3s ease;
    }

    /* 通用悬停效果 */
    .hover-lift:hover {
        transform: translateY(-2px);
    }

    .hover-scale:hover {
        transform: scale(1.02);
    }
}

/* 超大屏幕适配 (1200px+) */
@media screen and (min-width: 1200px) {
    .page-container,
    .cu-bar {
        max-width: 1000px;
    }
}
