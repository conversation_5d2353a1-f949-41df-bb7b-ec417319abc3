rich-text,mp-html {
	line-height: 40rpx;
}
.questionAsk-layout {
	display: flex;
	flex-direction: column;
	padding: 30rpx;
	background: white;
	font-size: 30rpx;
}
.options-layout {
	display: flex;
	flex-direction: column;
	background: white;
	font-size: 30rpx;
	padding-bottom: 30rpx;
}
.bottom-layout {
	position: fixed;
	height: 6%;
	left: 0;
	right: 0;
	bottom: 0;
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-around;
	z-index: 1000;
}
.options-layout rich-text,.options-layout mp-html {
	margin-left: 30rpx;
}
.layout-result {
	margin: 30rpx 30rpx 0 30rpx;
	padding: 30rpx 30rpx 30rpx 40rpx;
	display: flex;
	align-items: center;
	background: #f5f5f5;
	border-radius: 20rpx;
	font-size: 28rpx;
	color: #333;
}
.layout-result-correct {
	display: flex;
	align-items: center;
	background: #62d88b;
	color: white;
}
.explain-layout {
	padding: 30rpx;
	background: white;
}
.explain-label {
	font-size: 24rpx;
	font-weight: bold;
	color: #666;
}
.explain-answer {
	display: flex;
	flex-direction: row;
	align-items: center;
	font-size: 30rpx;
	margin-top: 30rpx;
	color: #333;
}
.explain-answer .correct {
	margin-left: 30rpx;
	color: #62d88b;
	font-weight: bold;
}
.explain-answer .error {
	margin-left: 30rpx;
	color: #fd7d7f;
	font-weight: bold;
}
.explain-answer .answer {
	color: #333;
}
.explain-layout .explain-text {
	font-size: 30rpx;
	color: #333;
}

/* 引入统一响应式适配 */


