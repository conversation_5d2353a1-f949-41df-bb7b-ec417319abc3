rich-text,mp-html {
	line-height: 40rpx;
}
.questionAsk-layout {
	display: flex;
	flex-direction: column;
	padding: 30rpx;
	background: white;
	font-size: 30rpx;
}
.options-layout {
	display: flex;
	flex-direction: column;
	background: white;
	font-size: 30rpx;
	padding-bottom: 30rpx;
}
.bottom-layout {
	position: fixed;
	height: 6%;
	left: 0;
	right: 0;
	bottom: 0;
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-around;
	z-index: 1000;
}
.options-layout rich-text,.options-layout mp-html {
	margin-left: 30rpx;
}
.layout-result {
	margin: 30rpx 30rpx 0 30rpx;
	padding: 30rpx 30rpx 30rpx 40rpx;
	display: flex;
	align-items: center;
	background: #f5f5f5;
	border-radius: 20rpx;
	font-size: 28rpx;
	color: #333;
}
.layout-result-correct {
	display: flex;
	align-items: center;
	background: #62d88b;
	color: white;
}
.explain-layout {
	padding: 30rpx;
	background: white;
}
.explain-label {
	font-size: 24rpx;
	font-weight: bold;
	color: #666;
}
.explain-answer {
	display: flex;
	flex-direction: row;
	align-items: center;
	font-size: 30rpx;
	margin-top: 30rpx;
	color: #333;
}
.explain-answer .correct {
	margin-left: 30rpx;
	color: #62d88b;
	font-weight: bold;
}
.explain-answer .error {
	margin-left: 30rpx;
	color: #fd7d7f;
	font-weight: bold;
}
.explain-answer .answer {
	color: #333;
}
.explain-layout .explain-text {
	font-size: 30rpx;
	color: #333;
}

/* PC端适配样式 */
@media screen and (min-width: 768px) {
	/* 页面背景 */
page {
		background-color: #f5f5f5;
}

	/* 顶部标题栏适配 */
.cu-custom {
		max-width: 1200px;
		margin: 0 auto;
}

	/* 主容器适配 */
view:first-child {
		max-width: 1000px;
		margin: 0 auto;
		background-color: #f5f5f5;
		min-height: 100vh;
}

	/* 滚动视图适配 */
scroll-view {
		max-width: 1000px;
		margin: 0 auto !important;
		background-color: #f5f5f5;
}

	/* 题目来源标签容器 */
.bg-white.questionAsk-layout {
		max-width: 900px;
		margin: 20px auto;
		padding: 24px;
		border-radius: 12px;
		box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

	/* 题目内容区域适配 */
.questionAsk-layout {
		max-width: 900px;
		margin: 20px auto;
		padding: 32px;
		border-radius: 12px;
		box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
		font-size: 16px;
}

	/* 选项区域适配 */
.options-layout {
		max-width: 900px;
		margin: 20px auto;
		padding: 0;
		background: transparent;
		font-size: 16px;
}

	/* 选项结果布局适配 */
.layout-result {
		max-width: 900px;
		margin: 12px auto;
		padding: 20px 24px;
		border-radius: 12px;
		font-size: 16px;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
		background: white;
		transition: all 0.3s ease;
}
.layout-result:hover {
		-webkit-transform: translateY(-2px);
		        transform: translateY(-2px);
		box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}
.layout-result-correct {
		background: #e8f5e8 !important;
		border-left: 4px solid #4caf50;
}

	/* 解析区域适配 */
.explain-layout {
		max-width: 900px;
		margin: 20px auto;
		padding: 32px;
		border-radius: 12px;
		box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

	/* 底部按钮适配 */
.bottom-layout {
		position: relative;
		max-width: 900px;
		margin: 20px auto 40px;
		border-radius: 12px;
		box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
		background: white;
		height: auto;
		padding: 20px;
}

	/* 题目标签适配 */
.cu-capsule {
		margin-bottom: 16px;
}
.cu-tag {
		font-size: 14px;
		padding: 6px 12px;
		border-radius: 6px;
}

	/* 字体大小适配 */
.explain-label {
		font-size: 16px;
		font-weight: 600;
}
.explain-answer {
		font-size: 16px;
		margin-top: 16px;
}
.explain-text {
		font-size: 16px !important;
		line-height: 1.8;
}

	/* rich-text适配 */
rich-text, mp-html {
		line-height: 1.8;
		font-size: 16px;
}

	/* 选项样式优化 */
.options-layout rich-text,
	.options-layout mp-html {
		margin-left: 16px;
		font-size: 16px;
}

	/* 选项文字对齐 */
.layout-result {
		display: flex;
		align-items: flex-start;
		gap: 12px;
}
.layout-result text:first-child {
		font-weight: 600;
		color: #1976d2;
		min-width: 24px;
		flex-shrink: 0;
}
}
