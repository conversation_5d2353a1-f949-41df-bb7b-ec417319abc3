.article-container {
    background-color: #f8f8f8;
    min-height: 100vh;
}

.article-wrapper {
    padding-bottom: 270rpx;
}

.article-content {
    background-color: #fff;
    margin: 10rpx; 
    border-radius: 12rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
    padding: 40rpx 30rpx;
}

.article-header {
    margin-bottom: 30rpx;
}

.article-title {
    font-size: 40rpx;
    font-weight: 700;
    color: #333;
    line-height: 1.4;
    display: block;
    margin-bottom: 20rpx;
}

.article-meta {
    display: flex;
    flex-wrap: wrap;
    font-size: 24rpx;
    color: #999;
    margin-top: 20rpx;
}

.meta-item {
    display: flex;
    align-items: center;
    margin-right: 30rpx;
    margin-bottom: 10rpx;
}

.meta-item text {
    margin-right: 6rpx;
}

.article-summary {
    font-size: 28rpx;
    color: #666;
    background-color: #f5f7fa;
    padding: 20rpx 30rpx;
    border-left: 8rpx solid #3a86ff;
    border-radius: 6rpx;
    margin: 20rpx 0 40rpx;
    line-height: 1.6;
}

.article-body {
    font-size: 30rpx;
    color: #333;
    line-height: 1.8;
    letter-spacing: 0.5rpx;
    margin-bottom: 40rpx;
}

.article-body >>> p {
    margin-bottom: 20rpx;
    text-indent: 2em;
}

.article-body >>> img {
    max-width: 100%;
    height: auto;
    margin: 20rpx 0;
    border-radius: 8rpx;
}

.article-tags {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    margin: 30rpx 0;
    font-size: 24rpx;
}

.tag-title {
    color: #666;
    margin-right: 10rpx;
}

.tag-item {
    background-color: #eef2fd;
    color: #3a86ff;
    padding: 6rpx 20rpx;
    border-radius: 30rpx;
    margin-right: 16rpx;
    margin-bottom: 16rpx;
}

.article-actions {
    display: flex;
    justify-content: center;
    margin-top: 50rpx;
    padding-top: 30rpx;
    border-top: 1rpx solid #eee;
}

.action-btn {
    background-color: #3a86ff;
    color: #fff;
    font-size: 28rpx;
    padding: 16rpx 40rpx;
    border-radius: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
}

.action-btn text {
    margin: 0 6rpx;
}

.share-btn {
    background-color: #3a86ff;
    color: #fff;
}

/* PC端响应式样式 */
@media screen and (min-width: 768px) {
    /* 整体容器PC端适配 */
    .article-container {
        background-color: #f5f5f5;
        padding: 15px 0;
    }

    .article-wrapper {
        max-width: 900px;
        margin: 0 auto;
        padding-bottom: 150px;
    }

    /* 文章内容PC端适配 */
    .article-content {
        margin: 0;
        border-radius: 8px;
        padding: 30px 40px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    /* 文章标题PC端适配 */
    .article-header {
        margin-bottom: 20px;
    }

    .article-title {
        font-size: 24px;
        margin-bottom: 16px;
    }

    /* 文章元信息PC端适配 */
    .article-meta {
        font-size: 12px;
        margin-top: 16px;
    }

    .meta-item {
        margin-right: 20px;
        margin-bottom: 8px;
    }

    /* 文章摘要PC端适配 */
    .article-summary {
        font-size: 14px;
        padding: 16px 20px;
        border-left: 4px solid #3a86ff;
        border-radius: 4px;
        margin: 16px 0 30px;
    }

    /* 文章正文PC端适配 */
    .article-body {
        font-size: 15px;
        line-height: 1.7;
        letter-spacing: 0.3px;
        margin-bottom: 30px;
    }

    .article-body >>> p {
        margin-bottom: 16px;
    }

    .article-body >>> img {
        margin: 16px 0;
        border-radius: 6px;
    }

    /* 标签PC端适配 */
    .article-tags {
        margin: 20px 0;
        font-size: 12px;
    }

    .tag-item {
        padding: 4px 12px;
        border-radius: 16px;
        margin-right: 12px;
        margin-bottom: 12px;
    }

    /* 操作按钮PC端适配 */
    .article-actions {
        margin-top: 40px;
        padding-top: 20px;
    }

    .action-btn {
        font-size: 14px;
        padding: 10px 24px;
        border-radius: 6px;
    }

    .action-btn text {
        margin: 0 4px;
    }
}
