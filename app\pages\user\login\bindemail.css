/* PC端响应式样式 */
@media screen and (min-width: 768px) {
    .page-container {
        max-width: 900px;
        margin: 15px auto;
        background-color: #ffffff;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.08);
        border-radius: 8px;
        min-height: calc(100vh - 30px);
        padding: 20px;
    }
    .cu-btn { font-size: 14px; padding: 8px 16px; border-radius: 6px; min-height: 36px; }
    .cu-form-group { margin: 12px 0; border-radius: 6px; }
    .cu-form-group input { font-size: 14px; padding: 10px 12px; }
    .text-xl { font-size: 18px !important; }
    .text-df { font-size: 14px !important; }
    .text-sm { font-size: 12px !important; }
}