.guide-container {
	padding: 30rpx;
	background: #fff;
}

.guide-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	text-align: center;
	margin-bottom: 40rpx;
}

.step-item {
	margin-bottom: 30rpx;
	background: #f8f8f8;
	border-radius: 12rpx;
	padding: 20rpx;
	display: flex;
	align-items: center;
}

.step-header {
	display: flex;
	align-items: center;
}

.step-number {
	display: inline-flex;
	justify-content: center;
	align-items: center;
	width: 24px;
	height: 24px;
	background-color: #0081ff;
	color: #fff;
	border-radius: 50%;
	margin-right: 10px;
}

.step-title {
	line-height: 1.6;
	padding-left: 6rpx;
	margin-bottom: 10rpx;
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
}

.step-content {
	font-size: 28rpx;
	color: #666;
	line-height: 1.6;
	padding-left: 8rpx;
}

.tips {
	margin-top: 40rpx;
	background: #f0f7ff;
	padding: 20rpx;
	border-radius: 12rpx;
}

.tips-title {
	font-size: 28rpx;
	color: #2979ff;
	font-weight: bold;
	margin-bottom: 10rpx;
}

.tips-content {
	font-size: 26rpx;
	color: #666;
	line-height: 1.6;
}

/* PC端适配样式 */
@media screen and (min-width: 768px) {
	/* 页面背景 */
	page {
		background-color: #f5f5f5;
	}

	/* 顶部标题栏适配 */
	.cu-custom {
		max-width: 1200px;
		margin: 0 auto;
	}

	/* 主容器适配 */
	.flex.flex-direction {
		max-width: 800px;
		margin: 0 auto;
		background-color: #f5f5f5;
		min-height: calc(100vh - 20px);
		margin-top: 20px;
	}

	/* 指南容器适配 */
	.guide-container {
		max-width: 800px;
		margin: 20px auto;
		padding: 40px;
		border-radius: 12px;
		box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
	}

	/* 指南标题适配 */
	.guide-title {
		font-size: 24px;
		margin-bottom: 32px;
	}

	/* 步骤项适配 */
	.step-item {
		margin-bottom: 24px;
		padding: 24px;
		border-radius: 12px;
		transition: all 0.3s ease;
		cursor: default;
	}

	.step-item:hover {
		box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
		transform: translateY(-2px);
	}

	/* 步骤编号适配 */
	.step-number {
		width: 32px;
		height: 32px;
		font-size: 16px;
		margin-right: 16px;
	}

	/* 步骤标题适配 */
	.step-title {
		font-size: 18px;
		margin-bottom: 8px;
		padding-left: 0;
	}

	/* 步骤内容适配 */
	.step-content {
		font-size: 16px;
		line-height: 1.8;
		padding-left: 0;
	}

	/* 提示区域适配 */
	.tips {
		margin-top: 32px;
		padding: 24px;
		border-radius: 12px;
		background: linear-gradient(135deg, #f0f7ff 0%, #e3f2fd 100%);
	}

	/* 提示标题适配 */
	.tips-title {
		font-size: 18px;
		margin-bottom: 12px;
	}

	/* 提示内容适配 */
	.tips-content {
		font-size: 16px;
		line-height: 1.8;
	}
}