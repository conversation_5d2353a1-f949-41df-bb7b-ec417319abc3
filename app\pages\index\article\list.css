/* 引入统一响应式适配 */
@import url("../../../common/css/responsive.css");

/* 文章列表页面特定的PC端适配 - 基于统一标准 */
@media (min-width: 768px) {
    /* 整体容器PC端适配 - 对标首页1000px标准 */
    .list-container {
        max-width: 1000px;
        margin: 20px auto;
        background-color: #ffffff;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        border-radius: 12px;
        min-height: calc(100vh - 40px);
        overflow: hidden;
    }

    /* 分类导航PC端适配 - 对标首页标准 */
    .nav {
        padding: 8px 20px;
        border-radius: 12px 12px 0 0;
    }

    .nav .cu-item {
        font-size: 14px;
        padding: 8px 16px;
    }

    /* 滚动区域PC端适配 - 对标首页标准 */
    .scroll-Y {
        padding: 0 20px;
    }

    /* 文章卡片PC端适配 - 对标首页标准 */
    .cu-card.article .cu-item {
        margin: 10px 0;
        border-radius: 8px;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .cu-card.article .cu-item:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.12);
    }

    .cu-card.article .content {
        padding: 12px 16px;
    }

    .cu-card.article .content image {
        width: 50px;
        height: 50px;
        border-radius: 6px;
    }

    .cu-card.article .desc {
        margin-left: 12px;
    }

    .cu-card.article .text-df {
        font-size: 13px;
        line-height: 1.4;
        margin-bottom: 6px;
    }

    .cu-card.article .text-sm {
        font-size: 11px;
    }
}