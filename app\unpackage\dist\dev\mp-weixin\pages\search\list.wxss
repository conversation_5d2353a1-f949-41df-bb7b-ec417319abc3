.questionAsk-layout {
	display: flex;
	flex-direction: column;
	padding: 30rpx;
	background: white;
	font-size: 30rpx;
}
.options-layout {
	display: flex;
	flex-direction: column;
	background: white;
	font-size: 10rpx;
	padding-bottom: 10rpx;
}
.bottom-layout {
	position: fixed;
	height: 6%;
	left: 0;
	right: 0;
	bottom: 0;
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-around;
	z-index: 1000;
}
.options-layout rich-text,.options-layout mp-html {
	margin-left: 30rpx;
}
.layout-result {
	margin: 15rpx 15rpx 0 15rpx;
	padding: 20rpx;
	display: flex;
	align-items: center;
	background: #f5f5f5;
	border-radius: 20rpx;
	font-size: 30rpx;
	color: #333;
}
.layout-result-correct {
	display: flex;
	align-items: center;
	background: #62d88b;
	color: white;
}
.explain-layout {
	padding: 30rpx;
	background: white;
}
.explain-label {
	font-size: 24rpx;
	font-weight: bold;
	color: #666;
}
.explain-answer {
	display: flex;
	flex-direction: row;
	align-items: center;
	font-size: 30rpx;
	color: #333;
}
.explain-answer .correct {
	margin-left: 30rpx;
	color: #62d88b;
	font-weight: bold;
}
.explain-answer .error {
	margin-left: 30rpx;
	color: #fd7d7f;
	font-weight: bold;
}
.explain-answer .answer {
	color: #333;
}
.explain-layout .explain-text {
	font-size: 30rpx;
	color: #333;
}
/* 引入统一响应式适配 */
/* 搜索列表页面特定的PC端适配 - 基于统一标准 */
@media (min-width: 768px) {
	/* 整体页面容器PC端适配 - 对标首页1000px标准 */
.search-list-container {
		max-width: 1000px;
		margin: 20px auto;
		background-color: #ffffff;
		box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
		border-radius: 12px;
		min-height: calc(100vh - 40px);
}

	/* 题目区域PC端适配 - 对标首页标准 */
.questionAsk-layout {
		padding: 20px;
		font-size: 15px;
		border-radius: 12px 12px 0 0;
}

	/* 选项区域PC端适配 - 对标首页标准 */
.options-layout {
		font-size: 14px;
		padding: 0 20px 10px;
}
.options-layout rich-text,
	.options-layout mp-html {
		margin-left: 20px;
}
.layout-result {
		margin: 8px 10px 0 10px;
		padding: 12px 16px;
		border-radius: 6px;
		font-size: 14px;
}

	/* 底部操作栏PC端适配 */
.bottom-layout {
		position: relative;
		height: auto;
		max-width: 900px;
		margin: 0 auto;
		padding: 15px 20px;
		border-radius: 0 0 8px 8px;
		background-color: #ffffff;
		box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}

	/* 解析区域PC端适配 */
.explain-layout {
		padding: 20px;
		border-radius: 0 0 8px 8px;
}
.explain-label {
		font-size: 12px;
}
.explain-answer {
		font-size: 14px;
}
.explain-answer .correct,
	.explain-answer .error {
		margin-left: 20px;
}
.explain-layout .explain-text {
		font-size: 14px;
		line-height: 1.6;
}

	/* 按钮PC端适配 */
.cu-btn {
		font-size: 14px;
		padding: 8px 16px;
		border-radius: 6px;
		min-height: 36px;
}
}
