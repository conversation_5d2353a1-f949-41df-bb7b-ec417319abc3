rich-text,mp-html {
	line-height: 40rpx;
}

.questionAsk-layout {
	display: flex;
	flex-direction: column;
	padding: 30rpx;
	background: white;
	font-size: 30rpx;
}

.options-layout {
	display: flex;
	flex-direction: column;
	background: white;
	font-size: 30rpx;
	padding-bottom: 30rpx;
}

.bottom-layout {
	position: fixed;
	height: 6%;
	left: 0;
	right: 0;
	bottom: 0;
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-around;
	z-index: 1000;
}

.options-layout rich-text,.options-layout mp-html {
	margin-left: 30rpx;
}

.layout-result {
	margin: 30rpx 30rpx 0 30rpx;
	padding: 30rpx 30rpx 30rpx 40rpx;
	display: flex;
	align-items: center;
	background: #f5f5f5;
	border-radius: 20rpx;
	font-size: 28rpx;
	color: #333;
}

.layout-result-correct {
	display: flex;
	align-items: center;
	background: #62d88b;
	color: white;
}


.explain-layout {
	padding: 30rpx;
	background: white;
}

.explain-label {
	font-size: 24rpx;
	font-weight: bold;
	color: #666;
}

.explain-answer {
	display: flex;
	flex-direction: row;
	align-items: center;
	font-size: 30rpx;
	margin-top: 30rpx;
	color: #333;
}

.explain-answer .correct {
	margin-left: 30rpx;
	color: #62d88b;
	font-weight: bold;
}

.explain-answer .error {
	margin-left: 30rpx;
	color: #fd7d7f;
	font-weight: bold;
}

.explain-answer .answer {
	color: #333;
}

.explain-layout .explain-text {
	font-size: 30rpx;
	color: #333;
}

/* PC端适配样式 - 按照图二的样式，保持原有布局 */
@media (min-width: 768px) {
	/* 顶部标题栏PC端适配 */
	.back-container {
		height: 60px !important;
	}

	.back-container .back-bar {
		min-height: 60px !important;
		padding: 0 15px;
	}

	.back-container .content text {
		font-size: 16px !important;
	}

	/* 题目内容区域PC端适配 - 保持原有布局，只调整尺寸 */
	.questionAsk-layout {
		max-width: 700px;
		margin: 8px auto;
		padding: 12px 16px;
		border-radius: 6px;
		font-size: 13px;
	}

	/* 选项区域PC端适配 - 保持原有布局 */
	.options-layout {
		max-width: 700px;
		margin: 0 auto;
		padding: 0 20px;
		font-size: 13px;
	}

	/* 选项结果布局适配 - 保持原有样式 */
	.layout-result {
		margin-bottom: 10px;
		padding: 12px 16px;
		border-radius: 6px;
		font-size: 13px;
		transition: all 0.3s ease;
		cursor: pointer;
	}

	.layout-result:hover {
		transform: translateY(-1px);
		box-shadow: 0 4px 15px rgba(0, 0, 0, 0.12);
	}

	/* 解析区域PC端适配 */
	.explain-layout {
		max-width: 700px;
		margin: 8px auto;
		padding: 12px 16px;
		border-radius: 6px;
	}

	/* 底部按钮适配 */
	.bottom-layout {
		max-width: 700px;
		margin: 8px auto;
		border-radius: 6px;
		padding: 8px 12px;
	}

	/* 题目标签适配 */
	.cu-capsule {
		margin-bottom: 10px;
	}

	.cu-tag {
		font-size: 12px;
		padding: 4px 8px;
		border-radius: 4px;
	}

	/* 字体大小适配 */
	.explain-label {
		font-size: 12px;
	}

	.explain-answer {
		font-size: 13px;
	}

	.explain-text {
		font-size: 13px !important;
		line-height: 1.4;
	}

	/* rich-text适配 */
	rich-text, mp-html {
		line-height: 1.4;
		font-size: 13px;
	}

	/* 选项样式优化 */
	.options-layout rich-text,
	.options-layout mp-html {
		margin-left: 12px;
		font-size: 13px;
	}
}

/* PC端适配样式 - 按照首页标准 */
@media (min-width: 768px) {
	/* 顶部标题栏PC端适配 - 整体缩小 */
	.back-container {
		height: 60px !important;
		max-width: 1200px;
		margin: 0 auto;
	}

	.back-container .back-bar {
		min-height: 60px !important;
		padding: 0 15px;
	}

	.back-container .content text {
		font-size: 16px !important;
	}

	/* 题目内容区域PC端适配 - 整体缩小 */
	.questionAsk-layout {
		max-width: 700px;
		margin: 8px auto;
		padding: 15px;
		border-radius: 8px;
		box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
		font-size: 13px;
	}

	/* 选项区域PC端适配 - 整体缩小 */
	.options-layout {
		max-width: 700px;
		margin: 8px auto;
		padding: 15px;
		border-radius: 8px;
		box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
		font-size: 13px;
	}

	/* 解析区域PC端适配 - 整体缩小 */
	.explain-layout {
		max-width: 700px;
		margin: 8px auto;
		padding: 15px;
		border-radius: 8px;
		box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
	}

	/* 底部按钮适配 - 整体缩小 */
	.bottom-layout {
		position: relative;
		max-width: 700px;
		margin: 8px auto;
		border-radius: 8px;
		box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
		background: white;
		height: auto;
		padding: 12px;
	}

	/* 选项结果布局适配 - 整体缩小 */
	.layout-result {
		max-width: 700px;
		margin: 8px auto;
		padding: 12px 16px;
		border-radius: 8px;
		font-size: 13px;
		box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
		transition: all 0.3s ease;
		cursor: pointer;
	}

	.layout-result:hover {
		transform: translateY(-1px);
		box-shadow: 0 4px 15px rgba(0, 0, 0, 0.12);
	}

	/* 题目标签适配 - 整体缩小 */
	.cu-capsule {
		max-width: 700px;
		margin: 0 auto;
	}

	.cu-tag {
		font-size: 12px;
		padding: 4px 8px;
		border-radius: 4px;
	}

	/* 字体大小适配 - 缩小 */
	.explain-label {
		font-size: 12px;
	}

	.explain-answer {
		font-size: 13px;
	}

	.explain-text {
		font-size: 13px !important;
		line-height: 1.4;
	}

	/* rich-text适配 - 缩小 */
	rich-text, mp-html {
		line-height: 1.4;
		font-size: 13px;
	}

	/* 选项样式优化 - 缩小 */
	.options-layout rich-text,
	.options-layout mp-html {
		margin-left: 12px;
		font-size: 13px;
	}
}