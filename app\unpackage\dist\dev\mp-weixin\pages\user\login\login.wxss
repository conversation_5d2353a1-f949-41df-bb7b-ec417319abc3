/* PC端响应式样式 */
@media screen and (min-width: 768px) {
    /* 整体登录页面容器PC端适配 */
.login-container {
        max-width: 900px;
        margin: 15px auto;
        background-color: #ffffff;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.08);
        border-radius: 8px;
        min-height: calc(100vh - 30px);
        padding: 40px;
        display: flex;
        flex-direction: column;
        justify-content: center;
}

    /* 登录表单PC端适配 */
.login-form {
        max-width: 400px;
        margin: 0 auto;
}

    /* 表单组PC端适配 */
.cu-form-group {
        margin: 16px 0;
        border-radius: 6px;
}
.cu-form-group input {
        font-size: 14px;
        padding: 12px 16px;
}

    /* 登录按钮PC端适配 */
.cu-btn.lg {
        font-size: 16px;
        padding: 12px 24px;
        border-radius: 6px;
        min-height: 48px;
        margin: 20px 0;
}

    /* 其他登录方式PC端适配 */
.other-login {
        text-align: center;
        margin-top: 30px;
}
.other-login .cu-btn {
        font-size: 14px;
        padding: 8px 16px;
        margin: 8px;
        border-radius: 6px;
        min-height: 36px;
}

    /* 标题PC端适配 */
.login-title {
        text-align: center;
        font-size: 24px;
        margin-bottom: 30px;
        color: #333;
}

    /* 链接PC端适配 */
.login-links {
        text-align: center;
        margin-top: 20px;
}
.login-links text {
        font-size: 12px;
        color: #666;
        margin: 0 8px;
        cursor: pointer;
}
.login-links text:hover {
        color: #0081ff;
}
}
