/* 引入统一响应式适配 */
/*
 * 统一响应式适配CSS - 完全对标首页标准
 * 为整个项目提供统一的PC端适配
 * 基于 app/pages/index/index.css 的响应式设计标准
 */
@media (min-width: 768px) {
	/* 页面整体布局 - 完全对标首页 */
page {
		background-color: #f5f7fa;
}

	/* 主容器适配 - 完全对标首页 */
.pc-page {
		max-width: 1200px;
		margin: 0 auto;
		background-color: #f5f7fa;
		min-height: 100vh;
		padding: 0 20px;
}
.pc-content {
		max-width: 1000px;
		margin: 0 auto;
		padding: 0;
}

	/* 顶部标题栏PC端适配 - 完全对标首页 */
.cu-custom {
		max-width: 1200px;
		margin: 0 auto;
}
.back-container {
		height: 64px !important;
		max-width: 1200px;
		margin: 0 auto;
}
.back-container .back-bar {
		min-height: 64px !important;
		padding: 0 20px;
}
.back-container .content text {
		font-size: 18px !important;
		font-weight: 500;
}

	/* 通用内容区域适配 - 对标首页1000px标准 */
.content-container,
	.main-content,
	.page-content,
	.questionAsk-layout,
	.options-layout,
	.explain-layout {
		max-width: 1000px;
		margin-left: auto;
		margin-right: auto;
}

	/* 卡片容器适配 - 对标首页标准 */
.cu-card {
		max-width: 1000px;
		margin: 20px auto;
		padding: 0;
		background: white;
		border-radius: 12px;
		box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}
.cu-card .cu-item {
		margin-bottom: 0;
		border-radius: 0;
		transition: all 0.3s ease;
		cursor: pointer;
		border-bottom: 1px solid #f0f0f0;
}
.cu-card .cu-item:hover {
		-webkit-transform: translateY(-1px);
		        transform: translateY(-1px);
		box-shadow: 0 4px 15px rgba(0, 0, 0, 0.12);
}
.cu-card .cu-item:last-child {
		border-bottom: none;
		border-radius: 0 0 12px 12px;
}
.cu-card .cu-item:first-child {
		border-radius: 12px 12px 0 0;
}

	/* 列表容器适配 - 对标首页标准 */
.cu-list {
		max-width: 1000px;
		margin-left: auto;
		margin-right: auto;
}

	/* 表单容器适配 - 对标首页标准 */
.cu-form-group {
		max-width: 600px;
		margin-left: auto;
		margin-right: auto;
}

	/* 底部固定栏适配 - 对标首页标准 */
.bottom-layout,
	.cu-bar.tabbar,
	.cu-bar.foot {
		left: 50%;
		-webkit-transform: translateX(-50%);
		        transform: translateX(-50%);
		max-width: 1000px;
		width: 100%;
}

	/* 搜索栏适配 - 完全对标首页 */
.cu-bar.search {
		max-width: 1000px;
		margin: 20px auto;
		padding: 16px 24px;
		min-height: auto;
		background: white;
		border-radius: 12px;
		box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
		display: flex;
		justify-content: space-between;
		align-items: center;
}
.cu-bar.search .action {
		flex: 1;
		font-size: 14px !important;
		padding: 12px 16px;
		border-radius: 8px;
		background-color: #f8f9fa;
		margin: 0 8px;
		transition: all 0.3s ease;
		display: flex;
		align-items: center;
		justify-content: center;
}
.cu-bar.search .action:hover {
		background-color: #e9ecef;
		-webkit-transform: translateY(-1px);
		        transform: translateY(-1px);
}

	/* 按钮适配 - 对标首页标准 */
.cu-btn {
		transition: all 0.3s ease;
		font-size: 14px;
		padding: 8px 16px;
		border-radius: 6px;
}
.cu-btn:hover {
		-webkit-transform: translateY(-1px);
		        transform: translateY(-1px);
		box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

	/* 图片适配 - 对标首页标准 */
.responsive-image {
		max-width: 100%;
		height: auto;
		border-radius: 8px;
}

	/* 文本适配 - 对标首页标准 */
.responsive-text {
		line-height: 1.6;
		font-size: 14px;
}
}
/* 超大屏幕适配 (1440px+) - 对标首页标准 */
@media (min-width: 1440px) {
.pc-page {
		max-width: 1400px;
		padding: 0 40px;
}
.pc-content {
		max-width: 1200px;
}
.back-container {
		max-width: 1400px;
}
.cu-custom {
		max-width: 1400px;
}
.content-container,
	.main-content,
	.page-content,
	.questionAsk-layout,
	.options-layout,
	.explain-layout,
	.cu-card,
	.cu-list {
		max-width: 1200px;
}
.bottom-layout,
	.cu-bar.tabbar,
	.cu-bar.foot {
		max-width: 1200px;
}
.cu-bar.search {
		max-width: 1200px;
}
}
/* 平板适配 (768px - 1023px) - 对标首页标准 */
@media (min-width: 768px) and (max-width: 1023px) {
.pc-page {
		padding: 0 15px;
}
.cu-form-group {
		max-width: 500px;
}
}
/* 小屏PC适配 (1024px - 1439px) - 对标首页标准 */
@media (min-width: 1024px) and (max-width: 1439px) {
.pc-page {
		padding: 0 30px;
}
.cu-form-group {
		max-width: 550px;
}
}
