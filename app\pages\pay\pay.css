/* 支付容器样式 */
.payment-container {
	padding: 30rpx;
	background-color: #f8f8f8;
}

/* 订单卡片样式 */
.order-card {
	background-color: #ffffff;
	border-radius: 12rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
	margin-bottom: 30rpx;
	overflow: hidden;
}

.order-header {
	padding: 20rpx 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.order-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333333;
}

.order-content {
	padding: 20rpx 30rpx;
}

.order-item {
	display: flex;
	align-items: center;
	padding: 16rpx 0;
	border-bottom: 1rpx solid #f8f8f8;
}

.order-item:last-child {
	border-bottom: none;
}

.item-label {
	color: #666666;
	margin: 0 16rpx;
}

.item-value {
	color: #333333;
	flex: 1;
}

.price-item {
	padding-top: 24rpx;
}

.price-value {
	color: #ff6600;
	font-size: 36rpx;
	font-weight: bold;
	flex: 1;
}

/* 支付方式区域 */
.payment-methods {
	background-color: #ffffff;
	border-radius: 12rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
	padding: 20rpx 30rpx;
	margin-bottom: 30rpx;
}

.section-title {
	display: flex;
	align-items: center;
	padding: 16rpx 0 24rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
	margin-bottom: 24rpx;
}

.section-title text {
	margin-right: 10rpx;
	font-size: 30rpx;
	color: #333333;
}

.payment-btn {
	background: linear-gradient(to right, #07c160, #10ad6e);
	color: #ffffff;
	border-radius: 50rpx;
	font-size: 32rpx;
	padding: 20rpx 0;
	margin: 10rpx 0;
	display: flex;
	justify-content: center;
	align-items: center;
}

/* 扫码支付区域 */
.scan-pay-container {
	background-color: #ffffff;
	border-radius: 12rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
	padding: 20rpx 30rpx;
	margin-bottom: 30rpx;
}

.qrcode-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 30rpx 0;
}

.qrcode-image {
	width: 400rpx;
	height: 400rpx;
	margin-bottom: 20rpx;
	border: 1rpx solid #f0f0f0;
	padding: 10rpx;
}

.qrcode-tip {
	display: flex;
	align-items: center;
	color: #666666;
	font-size: 28rpx;
	margin-top: 20rpx;
}

.confirm-btn {
	background: linear-gradient(to right, #1989fa, #0570db);
	color: #ffffff;
	border-radius: 50rpx;
	font-size: 32rpx;
	padding: 20rpx 0;
	margin: 20rpx 0 10rpx 0;
	display: flex;
	justify-content: center;
	align-items: center;
}

/* 支付说明 */
.payment-tips {
	display: flex;
	align-items: center;
	padding: 20rpx;
	background-color: #f8f8f8;
	border-radius: 8rpx;
	margin-top: 20rpx;
}

.tips-text {
	color: #999999;
	font-size: 26rpx;
	margin-left: 10rpx;
}

/* PC端响应式样式 */
@media screen and (min-width: 768px) {
    /* 支付容器PC端适配 */
    .payment-container {
        max-width: 900px;
        margin: 15px auto;
        padding: 20px;
        background-color: #f8f8f8;
        border-radius: 8px;
        min-height: calc(100vh - 30px);
    }

    /* 订单卡片PC端适配 */
    .order-card {
        border-radius: 8px;
        margin-bottom: 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .order-header {
        padding: 16px 20px;
    }

    .order-title {
        font-size: 18px;
    }

    .order-content {
        padding: 16px 20px;
    }

    .order-item {
        padding: 12px 0;
    }

    .item-label {
        font-size: 14px;
        margin: 0 12px;
    }

    .item-value {
        font-size: 14px;
    }

    .price-item {
        padding-top: 16px;
    }

    .price-value {
        font-size: 20px;
    }

    /* 支付方式区域PC端适配 */
    .payment-methods {
        border-radius: 8px;
        padding: 16px 20px;
        margin-bottom: 20px;
    }

    .section-title {
        padding: 12px 0 16px 0;
        margin-bottom: 16px;
    }

    .section-title text {
        font-size: 16px;
        margin-right: 8px;
    }

    .payment-btn {
        border-radius: 6px;
        font-size: 14px;
        padding: 12px 0;
        margin: 8px 0;
        min-height: 44px;
    }

    /* 扫码支付区域PC端适配 */
    .scan-pay-container {
        border-radius: 8px;
        padding: 16px 20px;
        margin-bottom: 20px;
    }

    .qrcode-container {
        padding: 20px 0;
    }

    .qrcode-image {
        width: 200px;
        height: 200px;
        margin-bottom: 12px;
        padding: 8px;
    }

    .qrcode-tip {
        font-size: 14px;
        margin-top: 12px;
    }

    .confirm-btn {
        border-radius: 6px;
        font-size: 14px;
        padding: 12px 0;
        margin: 16px 0 8px 0;
        min-height: 44px;
    }

    /* 支付说明PC端适配 */
    .payment-tips {
        padding: 12px 16px;
        border-radius: 6px;
        margin-top: 16px;
    }

    .tips-text {
        font-size: 12px;
        margin-left: 8px;
    }
}
