/* 结果页面容器 */
.result-container {
    padding: 30rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    min-height: calc(100vh - 200rpx);
}

/* 结果卡片 */
.result-card {
    width: 100%;
    border-radius: 20rpx;
    padding: 40rpx 30rpx;
    margin-bottom: 40rpx;
    position: relative;
    overflow: hidden;
}

.shadow {
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

/* 成绩圆环 */
.score-circle {
    width: 240rpx;
    height: 240rpx;
    margin: 20rpx auto 40rpx;
    position: relative;
}

.circle-bg {
    width: 240rpx;
    height: 240rpx;
    border-radius: 50%;
    background-color: #f5f5f5;
    position: relative;
    overflow: hidden;
}

.circle-progress {
    width: 120rpx;
    height: 240rpx;
    position: absolute;
    top: 0;
    left: 120rpx;
    background-color: #0081ff;
    transform-origin: left center;
}

.circle-progress::before {
    content: '';
    position: absolute;
    width: 240rpx;
    height: 240rpx;
    border-radius: 50%;
    background-color: #0081ff;
    top: 0;
    left: -120rpx;
}

.circle-content {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    background-color: white;
    transform: scale(0.85);
}

.circle-percent {
    font-size: 48rpx;
    font-weight: bold;
    color: #333;
}

.circle-label {
    font-size: 24rpx;
    color: #666;
    margin-top: 10rpx;
}

/* 详细数据 */
.result-stats {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 20rpx 0 30rpx;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0 40rpx;
}

.stat-value {
    font-size: 40rpx;
    font-weight: bold;
    color: #333;
}

.text-blue {
    color: #0081ff;
}

.stat-label {
    font-size: 24rpx;
    color: #666;
    margin-top: 10rpx;
}

.stat-divider {
    width: 2rpx;
    height: 60rpx;
    background-color: #eee;
}

/* 评价信息 */
.result-comment {
    text-align: center;
    padding: 20rpx 0;
    border-top: 1rpx solid #f0f0f0;
}

.comment-text {
    font-size: 30rpx;
    color: #666;
}

/* 操作按钮 */
.action-buttons {
    width: 100%;
    display: flex;
    flex-direction: column;
    padding: 20rpx 30rpx;
}

.action-buttons .cu-btn {
    margin-bottom: 20rpx;
    height: 90rpx;
    font-size: 32rpx;
    display: flex;
    align-items: center;
    justify-content: center;
}

.action-buttons .cuIcon-home,
.action-buttons .cuIcon-my {
    margin-right: 10rpx;
    font-size: 36rpx;
}

.margin-top {
    margin-top: 20rpx;
}

/* PC端响应式样式 */
@media screen and (min-width: 768px) {
    /* 结果页面容器PC端适配 */
    .result-container {
        max-width: 900px;
        margin: 15px auto;
        padding: 20px;
        background-color: #ffffff;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.08);
        border-radius: 8px;
        min-height: calc(100vh - 30px);
    }

    /* 结果卡片PC端适配 */
    .result-card {
        border-radius: 12px;
        padding: 30px 24px;
        margin-bottom: 30px;
    }

    /* 成绩圆环PC端适配 */
    .score-circle {
        width: 160px;
        height: 160px;
        margin: 16px auto 30px;
    }

    .circle-bg {
        width: 160px;
        height: 160px;
    }

    .circle-progress {
        width: 80px;
        height: 160px;
        left: 80px;
    }

    .score-text {
        font-size: 24px;
    }

    .score-label {
        font-size: 12px;
        margin-top: 8px;
    }

    /* 统计信息PC端适配 */
    .stats-grid {
        padding: 20px 0;
    }

    .stat-item {
        padding: 12px;
    }

    .stat-number {
        font-size: 18px;
    }

    .stat-label {
        font-size: 12px;
        margin-top: 6px;
    }

    /* 操作按钮PC端适配 */
    .action-buttons {
        margin-top: 30px;
    }

    .action-buttons .cu-btn {
        font-size: 14px;
        padding: 10px 20px;
        margin: 0 8px;
        border-radius: 6px;
        min-height: 40px;
    }

    .action-buttons .cuIcon-home,
    .action-buttons .cuIcon-my {
        margin-right: 6px;
        font-size: 16px;
    }

    .margin-top {
        margin-top: 16px;
    }
}

