.page-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background-color: #f8f8f8;
}

.content-container {
    flex: 1;
    padding: 20rpx;
    margin-bottom: 120rpx;
}

.course-list {
    padding: 10rpx;
}

.course-card {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    background-color: #ffffff;
    border-radius: 16rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.course-card:active {
    transform: scale(0.98);
    background-color: #f9f9f9;
}

.course-info {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.course-title {
    font-size: 32rpx;
    font-weight: 500;
    color: #333333;
    margin-bottom: 10rpx;
    line-height: 1.4;
}

.course-expire {
    font-size: 24rpx;
    color: #8a8a8a;
    line-height: 1.4;
}

.course-arrow {
    color: #c8c8cc;
    font-size: 36rpx;
    padding-left: 20rpx;
}

.course-state {
    font-size: 18rpx;
    color: white;
    padding: 7rpx 9rpx;
    border-radius: 10rpx;
}

/* PC端适配样式 */
@media screen and (min-width: 768px) {
    /* 页面背景 */
    page {
        background-color: #f5f5f5;
    }

    /* 顶部标题栏适配 */
    .cu-custom {
        max-width: 1200px;
        margin: 0 auto;
    }

    /* 页面容器适配 */
    .page-container {
        max-width: 800px;
        margin: 0 auto;
        background-color: #f5f5f5;
    }

    /* 内容容器适配 */
    .content-container {
        padding: 20px;
        margin-bottom: 0;
    }

    /* 课程列表适配 */
    .course-list {
        padding: 0;
    }

    /* 课程卡片适配 */
    .course-card {
        padding: 24px;
        margin-bottom: 16px;
        border-radius: 12px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        cursor: pointer;
    }

    .course-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        background-color: #ffffff;
    }

    .course-card:active {
        transform: scale(1);
    }

    /* 课程标题适配 */
    .course-title {
        font-size: 18px;
        margin-bottom: 8px;
    }

    /* 到期时间适配 */
    .course-expire {
        font-size: 14px;
    }

    /* 箭头适配 */
    .course-arrow {
        font-size: 20px;
        padding-left: 16px;
    }

    /* 课程状态适配 */
    .course-state {
        font-size: 12px;
        padding: 4px 8px;
        border-radius: 6px;
    }
}