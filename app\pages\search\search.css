.action-button {
	background-color: #f4f5f7;
	color: #222;
	margin: 0 5rpx;
	padding: 0;
}

/* PC端响应式样式 */
@media screen and (min-width: 768px) {
    /* 整体页面容器PC端适配 */
    .flex.flex-direction {
        max-width: 900px;
        margin: 0 auto;
        background-color: #ffffff;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.08);
        border-radius: 8px;
        margin-top: 15px;
        min-height: calc(100vh - 30px);
    }

    /* 搜索框PC端适配 */
    .cu-form-group {
        margin: 15px 20px;
        border-radius: 8px;
    }

    .cu-form-group textarea {
        font-size: 14px;
        padding: 12px 16px;
        min-height: 80px;
    }

    /* 功能按钮组PC端适配 */
    .grid.col-2 {
        max-width: 800px;
        margin: 0 auto;
        padding: 0 20px;
    }

    .cu-bar.btn-group {
        padding: 8px 0;
    }

    .action-button {
        font-size: 12px;
        padding: 8px 12px;
        margin: 0 4px;
        border-radius: 6px;
        min-height: 36px;
    }

    /* 搜索按钮PC端适配 */
    .padding-sm {
        padding: 15px 20px;
    }

    .cu-btn.lg {
        font-size: 14px;
        padding: 10px 20px;
        min-height: 44px;
        border-radius: 6px;
    }

    /* 搜索动态标题PC端适配 */
    .cu-bar {
        max-width: 900px;
        margin: 0 auto;
        padding: 8px 20px;
        min-height: 50px;
    }

    .cu-bar .action {
        font-size: 14px;
    }

    /* 搜索结果列表PC端适配 */
    .cu-list.menu {
        max-width: 900px;
        margin: 0 auto;
        padding: 0 20px;
    }

    .cu-list.menu .cu-item {
        padding: 12px 16px;
        font-size: 14px;
        border-radius: 6px;
        margin-bottom: 8px;
        transition: all 0.3s ease;
    }

    .cu-list.menu .cu-item:hover {
        background-color: #f8f9fa;
        transform: translateY(-1px);
    }

    /* 录音进度条PC端适配 */
    .padding.bg-white.margin-top-xs {
        max-width: 900px;
        margin: 15px auto;
        padding: 15px 20px;
        border-radius: 8px;
    }

    .cu-progress {
        height: 40px;
        border-radius: 6px;
    }

    .cu-progress .bg-green {
        font-size: 14px;
        line-height: 40px;
    }
}