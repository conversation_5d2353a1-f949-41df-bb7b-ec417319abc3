/* PC端响应式样式 */
@media screen and (min-width: 768px) {
    /* 整体页面容器PC端适配 */
    .info-container {
        max-width: 900px;
        margin: 15px auto;
        background-color: #ffffff;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.08);
        border-radius: 8px;
        min-height: calc(100vh - 30px);
        overflow: hidden;
    }

    /* 用户头像区域PC端适配 */
    .user-header {
        padding: 30px 40px;
        text-align: center;
    }

    .user-avatar {
        width: 80px;
        height: 80px;
        margin: 0 auto 16px;
    }

    .user-name {
        font-size: 18px;
        margin-bottom: 8px;
    }

    .user-level {
        font-size: 14px;
    }

    /* 信息列表PC端适配 */
    .cu-list.menu {
        padding: 0 40px;
    }

    .cu-list.menu .cu-item {
        padding: 12px 0;
        font-size: 14px;
    }

    .cu-list.menu .cu-item .content {
        font-size: 14px;
    }

    .cu-list.menu .cu-item .action {
        font-size: 12px;
    }

    /* 按钮PC端适配 */
    .cu-btn {
        font-size: 14px;
        padding: 10px 20px;
        margin: 20px 40px;
        border-radius: 6px;
        min-height: 40px;
    }
}