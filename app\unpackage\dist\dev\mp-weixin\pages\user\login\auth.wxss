/* PC端响应式样式 */
@media screen and (min-width: 768px) {
    /* 页面背景 */
page {
        background-color: #f5f5f5;
}

    /* 顶部标题栏适配 */
.cu-custom {
        max-width: 1200px;
        margin: 0 auto;
}

    /* 整体页面容器PC端适配 */
.page-container {
        max-width: 900px;
        margin: 15px auto;
        background-color: #ffffff;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.08);
        border-radius: 8px;
        min-height: calc(100vh - 30px);
        padding: 20px;
}

    /* 通用按钮PC端适配 */
.cu-btn {
        font-size: 14px;
        padding: 8px 16px;
        border-radius: 6px;
        min-height: 36px;
}
.cu-btn.lg {
        font-size: 16px;
        padding: 12px 24px;
        min-height: 48px;
}

    /* 通用表单PC端适配 */
.cu-form-group {
        margin: 12px 0;
        border-radius: 6px;
}
.cu-form-group input,
    .cu-form-group textarea {
        font-size: 14px;
        padding: 10px 12px;
}

    /* 通用文字大小PC端适配 */
.text-xl { font-size: 18px !important;
}
.text-lg { font-size: 16px !important;
}
.text-df { font-size: 14px !important;
}
.text-sm { font-size: 12px !important;
}
}
