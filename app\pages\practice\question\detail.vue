<template>
	<view v-if="isLoad" :class="pageClasses">
		<back :showBackText="false" customClass="bg-gradual-blue text-white" title="题目详情"></back>
		<view>
			<scroll-view :scrollY="true" :style="'height:calc(100vh - ' + CustomBar + 'px - 70px)'">
				<view class="bg-white questionAsk-layout" @tap="onCourseTap" style="padding-bottom: 0;">
					<view class="cu-capsule">
						<view class="cu-tag bg-blue">题目来源</view>
						<view class="cu-tag line-blue">{{item.course_name}}</view>
					</view>
				</view>
				<view v-if="item.background_id">
					<view class="questionAsk-layout">
						<rich-text :nodes="item.background_name" preview-img="true" />
					</view>
				</view>
				<view class="questionAsk-layout">
					<view>
						<rich-text :nodes="item.questionAsk" preview-img="true" />
					</view>
				</view>
				<view class="options-layout margin-top-xs"
					v-if="item.question_type == 1 || item.question_type == 2 || item.question_type == 3">
					<view :class="(item.correct_A == 1 ? 'layout-result-correct' : '') + ' layout-result'"
						data-answer="A" v-if="item.A">
						<text>A</text>
						<rich-text :nodes="item.A" />
					</view>
					<view :class="(item.correct_B == 1 ? 'layout-result-correct' : '') + ' layout-result'"
						data-answer="B" v-if="item.B">
						<text>B</text>
						<rich-text :nodes="item.B" />
					</view>
					<view :class="(item.correct_C == 1 ? 'layout-result-correct' : '') + ' layout-result'"
						data-answer="C" v-if="item.C">
						<text>C</text>
						<rich-text :nodes="item.C" />
					</view>
					<view :class="(item.correct_D == 1 ? 'layout-result-correct' : '') + ' layout-result'"
						data-answer="D" v-if="item.D">
						<text>D</text>
						<rich-text :nodes="item.D" />
					</view>
					<view :class="(item.correct_E == 1 ? 'layout-result-correct' : '') + ' layout-result'"
						data-answer="E" v-if="item.E">
						<text>E</text>
						<rich-text :nodes="item.E" />
					</view>
					<view :class="(item.correct_F == 1 ? 'layout-result-correct' : '') + ' layout-result'"
						data-answer="F" v-if="item.F">
						<text>F</text>
						<rich-text :nodes="item.F" />
					</view>
				</view>
				<view class="margin-top-xs">
					<view class="explain-layout ">
						<view class="explain-label">答案</view>
						<view style="display: flex">
							<view class="explain-answer">
								<view
									v-if="item.question_type == 1 || item.question_type == 2 || item.question_type == 3">
									参考答案</view>
								<rich-text
									:class="item.question_type == 1 || item.question_type == 2 || item.question_type == 3 ? 'correct' : 'answer'"
									:nodes="item.correctOption"></rich-text>
							</view>
						</view>
						<view class="explain-label" style="margin-top: 40rpx; padding-bottom: 30rpx"
							v-if="item.explanation">解析</view>
						<rich-text class="explain-text" :nodes="item.explanation" v-if="item.explanation"
							preview-img="true" />
						</rich-text>
					</view>
					<view>
						<advideo unitId="adunit-9099eb3969908c24" adTheme="blue"></advideo>
					</view>
				</view>
			</scroll-view>
		</view>
		<view class="bottom-layout cu-bar border tabbar bg-white">
			<view class="action text-blue" @tap="onCollectTap">
				<view class="cuIcon-likefill"></view> {{item.isCollection==1 ? '已收藏':'未收藏'}}
			</view>
			<view class="action text-blue" @tap="onCommentTap" v-if="!appIsAudit" data-type="1">
				<view class="cuIcon-commentfill"></view> 评论
			</view>
			<view class="action text-blue" @tap="onFeedbackTap" data-type="2">
				<view class="cuIcon-warnfill"></view>纠错
			</view>
			<button class="action text-blue" open-type="share">
				<view class="cuIcon-forwardfill"></view>
				转发
			</button>
		</view>
	</view>
</template>

<script src="./detail.js"></script>
<style src="./detail.css"></style>