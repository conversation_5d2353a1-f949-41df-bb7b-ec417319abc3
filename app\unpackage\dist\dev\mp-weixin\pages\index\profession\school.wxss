page {
    padding-top: 100rpx;
}
.indexes {
    position: relative;
}
.indexBar {
    position: fixed;
    right: 0px;
    bottom: 0px;
    padding: 20rpx 20rpx 20rpx 60rpx;
    display: flex;
    align-items: center;
}
.indexBar .indexBar-box {
    width: 40rpx;
    height: auto;
    background: #fff;
    display: flex;
    flex-direction: column;
    box-shadow: 0 0 20rpx rgba(0, 0, 0, 0.1);
    border-radius: 10rpx;
}
.indexBar-item {
    flex: 1;
    width: 40rpx;
    height: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24rpx;
    color: #888;
}
movable-view.indexBar-item {
    width: 40rpx;
    height: 40rpx;
    z-index: 9;
    position: relative;
}
movable-view.indexBar-item::before {
    content: '';
    display: block;
    position: absolute;
    left: 0;
    top: 10rpx;
    height: 20rpx;
    width: 4rpx;
    background-color: #f37b1d;
}
.indexToast {
    position: fixed;
    top: 0;
    right: 80rpx;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    width: 100rpx;
    height: 100rpx;
    border-radius: 10rpx;
    margin: auto;
    color: #fff;
    line-height: 100rpx;
    text-align: center;
    font-size: 48rpx;
}

/* PC端响应式样式 */
@media screen and (min-width: 768px) {
    /* 整体页面PC端适配 */
page {
        background-color: #f5f5f5;
        padding-top: 0;
}

    /* 学校列表容器PC端适配 */
.indexes {
        max-width: 900px;
        margin: 15px auto;
        background-color: #ffffff;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.08);
        border-radius: 8px;
        min-height: calc(100vh - 30px);
}

    /* 索引栏PC端适配 */
.indexBar {
        right: calc((100vw - 900px) / 2 - 60px);
        padding: 20px 20px 20px 40px;
}
.indexBar .indexBar-box {
        width: 30px;
        border-radius: 6px;
        box-shadow: 0 0 12px rgba(0, 0, 0, 0.1);
}
.indexBar-item {
        width: 30px;
        height: 30px;
        font-size: 12px;
}
movable-view.indexBar-item {
        width: 30px;
        height: 30px;
}
movable-view.indexBar-item::before {
        top: 6px;
        height: 12px;
        width: 3px;
}

    /* 索引提示PC端适配 */
.indexToast {
        right: calc((100vw - 900px) / 2 + 20px);
        width: 60px;
        height: 60px;
        border-radius: 6px;
        line-height: 60px;
        font-size: 24px;
}

    /* 学校列表项PC端适配 */
.cu-list.menu .cu-item {
        padding: 12px 20px;
        font-size: 14px;
}
.cu-list.menu .cu-item:hover {
        background-color: #f8f9fa;
}

    /* 分组标题PC端适配 */
.cu-bar {
        padding: 8px 20px;
        min-height: 40px;
}
.cu-bar .action {
        font-size: 14px;
}
}
